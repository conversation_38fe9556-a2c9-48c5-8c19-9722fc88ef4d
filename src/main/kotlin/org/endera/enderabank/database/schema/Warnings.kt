package org.endera.enderabank.database.schema

import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable

enum class WarningState(val string: String) {
    PENDING("Pending"),
    PAID("Paid"),
}

data class Warning(
    val id: Int,
    val playerName: String,
    val issuer: String,
    val amount: Int,
    val state: WarningState,
    val description: String,
)

class WarningEntity(id: EntityID<Int>): IntEntity(id) {
    companion object : IntEntityClass<WarningEntity>(Warnings)

    var playerName by Warnings.playerName
    var issuer by Warnings.issuer
    var amount by Warnings.amount
    var state by Warnings.state
    var description by Warnings.description


    fun toWarning() = Warning(
        id = id.value,
        playerName = playerName,
        issuer = issuer,
        amount = amount,
        state = state,
        description = description,
    )
}

object Warnings : IntIdTable() {
    val playerName = varchar("player_name", 64)
    val issuer = varchar("issuer", 64)
    val amount = integer("amount")
    val state = enumeration<WarningState>("state")
    val description = text("description")
}