package org.endera.enderabank.database.schema

import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable

enum class TransactionType {
    TRANSFER,
    DEPOSIT,
    PENALTY
}

data class Transaction(
    val id: Int,
    val transactionType: TransactionType,
    val amount: Int,
    val sender: String,
    val receiver: String,
    val senderAccount: Int,
    val receiverAccount: Int,
)

class TransactionEntity(id: EntityID<Int>): IntEntity(id) {
    companion object : IntEntityClass<TransactionEntity>(Transactions)

    var amount by Transactions.amount
    var transactionType by Transactions.transactionType
    var sender by Transactions.sender
    var receiver by Transactions.receiver
    var senderAccount by Transactions.senderAccount
    var receiverAccount by Transactions.receiverAccount

    fun toTransaction() = Transaction(
        id = id.value,
        transactionType = transactionType,
        amount = amount,
        sender = sender,
        receiver = receiver,
        senderAccount = senderAccount,
        receiverAccount = receiverAccount
    )
}

object Transactions : IntIdTable() {
    val amount = integer("amount")
    val transactionType = enumeration<TransactionType>("transaction_type")
    val sender = varchar("sender", 64)
    val receiver = varchar("receiver", 64)
    val senderAccount = integer("sender_account")
    val receiverAccount = integer("receiver_account")
}