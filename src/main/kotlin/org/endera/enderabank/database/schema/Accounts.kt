package org.endera.enderabank.database.schema

import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable

data class Account(
    val id: Int,
    val playerName: String,
    val subusers: List<String>,
    val balance: Int,
    val design: String,
    val isMain: Boolean,
    val isBlocked: Boolean,
)

class AccountEntity(id: EntityID<Int>): IntEntity(id) {
    companion object : IntEntityClass<AccountEntity>(Accounts)

    var playerName by Accounts.playerName
    var subusers by Accounts.subusers
    var balance by Accounts.balance
    var design by Accounts.design
    var isMain by Accounts.isMain
    var isBlocked by Accounts.isBlocked

    fun toAccount() = Account(
        id = id.value,
        playerName = playerName,
        subusers = subusers.split(","),
        balance = balance,
        design = design,
        isMain = isMain,
        isBlocked = isBlocked
    )
}

object Accounts : IntIdTable() {
    val playerName = varchar("player_name", 64)
    val subusers = varchar("subusers", 512)
    val balance = integer("balance")
    val design = varchar("design", 64)
    val isMain = bool("is_main").default(false)
    val isBlocked = bool("is_blocked").default(false)
}