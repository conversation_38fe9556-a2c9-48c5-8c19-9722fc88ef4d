package org.endera.enderabank.database

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.schema.Accounts
import org.endera.enderabank.database.schema.Transactions
import org.endera.enderabank.database.schema.Warnings
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File

lateinit var db: Database

fun initDb(dataFolder: File) {

    val storageType = config.storage.storageType.lowercase()
    println("Initializing database with storage type: $storageType")

    when (storageType) {
        "mysql" -> initMySQL()
        "postgresql" -> initPostgreSQL()
        "h2" -> initH2(dataFolder)
        "mariadb" -> initMariaDb()
        "sqlite" -> initSQLite(dataFolder)
        else -> initH2(dataFolder)
    }

    transaction(db) {
        println("Creating missing tables and columns if any...")
        SchemaUtils.createMissingTablesAndColumns(Accounts, Warnings, Transactions)
    }
}

private fun initSQLite(dataFolder: File) {
    println("Connecting to SQLite database...")
    db = Database.connect("jdbc:sqlite:${dataFolder.absolutePath}/sqlite.db", driver = "org.sqlite.JDBC")
    println("Initialized SQLite")
}

private fun initH2(dataFolder: File) {
    println("Connecting to H2 database...")
    val hikariConfig = HikariConfig().apply {
        jdbcUrl = "jdbc:h2:${dataFolder.absolutePath}/h2.db;DB_CLOSE_DELAY=-1"
        driverClassName = "org.h2.Driver"
        maximumPoolSize = 10
    }
    val dataSource = HikariDataSource(hikariConfig)
    db = Database.connect(dataSource)
    println("Initialized H2")
}

private fun initPostgreSQL() {
    println("Connecting to PostgreSQL database...")
    val hikariConfig = HikariConfig().apply {
        jdbcUrl = "jdbc:postgresql://${config.storage.remote.host}:${config.storage.remote.port}/${config.storage.remote.dbname}${config.storage.remote.connectionParameters}"
        driverClassName = "org.postgresql.Driver"
        username = config.storage.remote.user
        password = config.storage.remote.password
        maximumPoolSize = 10
    }
    val dataSource = HikariDataSource(hikariConfig)
    db = Database.connect(dataSource)
    println("Initialized PostgreSQL")
}

private fun initMySQL() {
    println("Connecting to MySQL database...")
    val hikariConfig = HikariConfig().apply {
        jdbcUrl = "jdbc:mysql://${config.storage.remote.host}:${config.storage.remote.port}/${config.storage.remote.dbname}${config.storage.remote.connectionParameters}"
        driverClassName = "com.mysql.cj.jdbc.Driver"
        username = config.storage.remote.user
        password = config.storage.remote.password
        maximumPoolSize = 10
    }
    val dataSource = HikariDataSource(hikariConfig)
    db = Database.connect(dataSource)
    println("Initialized MySQL")
}

private fun initMariaDb() {
    println("Connecting to MariaDB database...")
    val hikariConfig = HikariConfig().apply {
        jdbcUrl = "jdbc:mariadb://${config.storage.remote.host}:${config.storage.remote.port}/${config.storage.remote.dbname}${config.storage.remote.connectionParameters}"
        driverClassName = "org.mariadb.jdbc.Driver"
        username = config.storage.remote.user
        password = config.storage.remote.password
        maximumPoolSize = 10
    }
    val dataSource = HikariDataSource(hikariConfig)
    db = Database.connect(dataSource)
    println("Initialized MariaDB")
}
