package org.endera.enderabank.database.repository

import org.endera.enderabank.database.schema.WarningEntity
import org.endera.enderabank.database.schema.WarningState
import org.endera.enderabank.database.schema.Warnings
import org.endera.enderabank.dbQuery

class WarningsRepository {

    suspend fun createWarning(
        playerName: String,
        issuer: String,
        description: String,
        amount: Int
    ): WarningEntity {
        return dbQuery {
            WarningEntity.new {
                this.issuer = issuer
                this.description = description
                this.playerName = playerName
                this.amount = amount
                state = WarningState.PENDING
            }
        }
    }

    suspend fun setWarningState(id: Int, state: WarningState): WarningEntity? {
        return dbQuery {
            WarningEntity.findById(id)?.apply {
                this.state = state
            }
        }
    }

    suspend fun getWarningById(id: Int): WarningEntity? {
        return dbQuery {
            WarningEntity.findById(id)
        }
    }

    suspend fun getWarningsByPlayerName(playerName: String): List<WarningEntity> {
        return dbQuery {
            WarningEntity.find { Warnings.playerName eq playerName }.toList()
        }
    }

    suspend fun deleteWarning(id: Int) {
        dbQuery {
            WarningEntity.findById(id)?.delete()
        }
    }

}