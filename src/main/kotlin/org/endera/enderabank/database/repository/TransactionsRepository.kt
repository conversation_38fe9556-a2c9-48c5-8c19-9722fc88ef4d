package org.endera.enderabank.database.repository

import org.endera.enderabank.database.schema.TransactionEntity
import org.endera.enderabank.database.schema.TransactionType
import org.endera.enderabank.database.schema.Transactions
import org.endera.enderabank.dbQuery
import org.jetbrains.exposed.sql.or

class TransactionsRepository {

    suspend fun addTransaction(
        amount: Int,
        transactionType: TransactionType,
        sender: String,
        receiver: String,
        senderAccount: Int,
        receiverAccount: Int
    ): TransactionEntity {
        return dbQuery {
            TransactionEntity.new {
                this.amount = amount
                this.transactionType = transactionType
                this.sender = sender
                this.receiver = receiver
                this.senderAccount = senderAccount
                this.receiverAccount = receiverAccount
            }
        }
    }

    suspend fun getTransactionsByPlayerName(playerName: String): List<TransactionEntity> {
        return dbQuery {
            TransactionEntity.find { (Transactions.sender eq playerName) or (Transactions.receiver eq playerName) }.toList()
        }
    }

    suspend fun getAllTransactionsByCardIds(cardIds: List<Int>): List<TransactionEntity> {
        return dbQuery {
            TransactionEntity.find { (Transactions.senderAccount inList cardIds) or (Transactions.receiverAccount inList cardIds) }.toList()
        }
    }

    suspend fun getTransactionById(id: Int): TransactionEntity? {
        return dbQuery {
            TransactionEntity.findById(id)
        }
    }

    suspend fun deleteTransaction(id: Int) {
        dbQuery {
            TransactionEntity.findById(id)?.delete()
        }
    }

}