package org.endera.enderabank.database.repository

import org.endera.enderabank.database.schema.AccountEntity
import org.endera.enderabank.database.schema.Accounts
import org.endera.enderabank.dbQuery
import org.endera.enderabank.utils.arrayToString
import org.endera.enderabank.utils.stringToArray
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.update

class AccountsRepository {

    suspend fun createAccount(playerName: String, design: String, isMain: Boolean): AccountEntity {
        return dbQuery {
            AccountEntity.new {
                this.playerName = playerName
                this.design = design
                this.isMain = isMain
                subusers = ""
                balance = 0
                isBlocked = false
            }
        }
    }

    suspend fun makeAccountMain(id: Int): AccountEntity {
        return dbQuery {
            val account = getAccountById(id)!!
            Accounts.update({ Accounts.playerName eq account.playerName}) {
                it[isMain] = false
            }
            account.isMain = true
            account
        }
    }

    suspend fun changeDesign(id: Int, design: String): AccountEntity {
        return dbQuery {
            val card = AccountEntity.findById(id) ?: throw IllegalArgumentException("Entity not found")
            card.design = design
            card
        }
    }

    suspend fun addSubuser(entityId: Int, newElement: String): AccountEntity {
        return dbQuery {
            val entity = AccountEntity.findById(entityId) ?: throw IllegalArgumentException("Entity not found")
            val currentArray = stringToArray(entity.subusers)
            val updatedArray = currentArray + newElement
            entity.subusers = arrayToString(updatedArray)
            entity
        }
    }

    suspend fun getAllSubusersById(id: Int): List<String> {
        return dbQuery {
            val entity = AccountEntity.findById(id) ?: throw IllegalArgumentException("Entity not found")
            val currentArray = stringToArray(entity.subusers)
            currentArray
        }
    }

    suspend fun removeSubuser(entityId: Int, elementToRemove: String): AccountEntity {
        return dbQuery {
            val entity = AccountEntity.findById(entityId) ?: throw IllegalArgumentException("Entity not found")
            val currentArray = stringToArray(entity.subusers)
            val updatedArray = currentArray.filter { it != elementToRemove }
            entity.subusers = arrayToString(updatedArray)
            entity
        }
    }


    suspend fun addBalance(id: Int, amount: Int): AccountEntity? {
        return dbQuery {
            AccountEntity.findById(id)?.apply {
                balance += amount
            }
        }
    }

    suspend fun removeBalance(id: Int, amount: Int): AccountEntity? {
        return dbQuery {
            AccountEntity.findById(id)?.apply {
                balance -= amount
            }
        }
    }

    suspend fun getMainAccountByName(player: String): AccountEntity? {
        return dbQuery {
            AccountEntity.find { (Accounts.playerName eq player) and (Accounts.isMain eq true) }.firstOrNull()
        }
    }

    suspend fun getAccountById(id: Int): AccountEntity? {
        return dbQuery {
            AccountEntity.findById(id)
        }
    }

    suspend fun getAccountsByName(name: String): List<AccountEntity> {
        return dbQuery {
            AccountEntity.find { Accounts.playerName eq name }.toList()
        }
    }

    suspend fun getSubuserAccountsByName(name: String): List<AccountEntity> {
        return dbQuery {
            AccountEntity.all().filter {
                it.subusers.split(",").contains(name)
            }
        }
    }

    suspend fun blockAccount(id: Int): AccountEntity? {
        return dbQuery {
            AccountEntity.findById(id)?.apply {
                isBlocked = true
            }
        }
    }

    suspend fun unblockAccount(id: Int): AccountEntity? {
        return dbQuery {
            AccountEntity.findById(id)?.apply {
                isBlocked = false
            }
        }
    }

    suspend fun deleteAccount(id: Int) {
        dbQuery {
            AccountEntity.findById(id)?.delete()
        }
    }

}