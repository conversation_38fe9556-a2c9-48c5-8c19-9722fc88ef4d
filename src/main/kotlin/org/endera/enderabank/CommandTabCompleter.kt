package org.endera.enderabank

import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.bukkit.Bukkit
import org.bukkit.command.Command
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import org.bukkit.entity.Player
import org.endera.enderabank.commands.Perms.ADMIN_PERMISSION
import org.endera.enderabank.commands.Perms.CHANGE_DESIGN_PERMISSION
import org.endera.enderabank.commands.Perms.DEPOSIT_PERMISSION
import org.endera.enderabank.commands.Perms.FBI_PERMISSION
import org.endera.enderabank.commands.Perms.NEWCARD_PERMISSION
import org.endera.enderabank.commands.Perms.PAYMENT_PERMISSION
import org.endera.enderabank.commands.Perms.RELOAD_PERMISSION
import org.endera.enderabank.commands.Perms.SUBUSER_PERMISSION
import org.endera.enderabank.commands.Perms.VIEW_TRANSACTIONS_PERMISSION
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.WarningState
import org.endera.enderabank.utils.filterDesigns
import org.endera.enderalib.utils.async.ioDispatcher

class RbankTabCompleter : TabCompleter {
    override fun onTabComplete(
        sender: CommandSender,
        command: Command,
        alias: String,
        args: Array<out String>
    ): List<String> {
        if (sender !is Player) return emptyList() // Only players can use rbank commands

        return runBlocking {
            when (args.size) {
                1 -> getFirstLevelSuggestions(sender)
                2 -> getSecondLevelSuggestions(sender, args[0])
                3 -> getThirdLevelSuggestions(sender, args[0], args[1])
                4 -> getFourthLevelSuggestions(sender, args[0], args[1], args[2])
                else -> emptyList()
            }.filter { it.startsWith(args.last(), ignoreCase = true) }
        }
    }
}

fun getFirstLevelSuggestions(sender: Player): List<String> {
    val suggestions = mutableListOf<String>()
    if (sender.hasPermission(RELOAD_PERMISSION)) suggestions.add("reload")
    if (sender.hasPermission(NEWCARD_PERMISSION)) suggestions.add("newcard")
    if (sender.hasPermission(SUBUSER_PERMISSION)) suggestions.add("subuser")
    if (sender.hasPermission(CHANGE_DESIGN_PERMISSION)) suggestions.add("changedesign")
    if (sender.hasPermission(PAYMENT_PERMISSION)) {
        suggestions.add("pay")
        suggestions.add("transfer")
        suggestions.add("paywarn")
    }
    if (sender.hasPermission(FBI_PERMISSION)) {
        suggestions.add("warn")
        suggestions.add("unwarn")
        suggestions.add("warns")
    }
    if (sender.hasPermission(VIEW_TRANSACTIONS_PERMISSION)) suggestions.add("transactions")
    if (sender.hasPermission(DEPOSIT_PERMISSION)) suggestions.add("deposit")
    if (sender.hasPermission(ADMIN_PERMISSION)) suggestions.add("take")
    return suggestions
}

private suspend fun getSecondLevelSuggestions(sender: Player, firstArg: String): List<String> {
    return when (firstArg) {
        "subuser" -> {
            if (sender.hasPermission(SUBUSER_PERMISSION)) {
                listOf("add", "remove")
            } else emptyList()
        }
        "newcard" -> {
            if (sender.hasPermission(NEWCARD_PERMISSION)) {
                sender.filterDesigns()
            } else emptyList()
        }
        "changedesign" -> {
            if (sender.hasPermission(CHANGE_DESIGN_PERMISSION)) {
                withContext(ioDispatcher) {
                    DBHolder.accountsRepository.getAccountsByName(sender.name)
                        .map { it.id.value.toString() }.sorted()
                }
            } else emptyList()
        }
        "transfer" -> {
            if (sender.hasPermission(PAYMENT_PERMISSION)) {
                withContext(ioDispatcher) {
                    (
                    DBHolder.accountsRepository.getAccountsByName(sender.name)
                        .map { it.id.value.toString() }
                            +
                    DBHolder.accountsRepository.getSubuserAccountsByName(sender.name)
                        .map { it.id.value.toString() }
                    ).sorted()
                }
            } else emptyList()
        }
        "pay" -> {
            if (sender.hasPermission(PAYMENT_PERMISSION)) {
                Bukkit.getOnlinePlayers().mapNotNull { it.name }
            } else emptyList()
        }
        "paywarn" -> {
            withContext(ioDispatcher) {
                if (sender.hasPermission(PAYMENT_PERMISSION)) {
                    DBHolder.warningsRepository.getWarningsByPlayerName(sender.name)
                        .filter { it.state == WarningState.PENDING }
                        .map { it.id.value.toString() }
                } else emptyList()
            }
        }
        "warn", "unwarn" -> {
            if (sender.hasPermission(FBI_PERMISSION)) {
                Bukkit.getOnlinePlayers().mapNotNull { it.name }
            } else emptyList()
        }
        "warns" -> {
            if (sender.hasPermission(FBI_PERMISSION)) {
                Bukkit.getOnlinePlayers().mapNotNull { it.name }
            } else emptyList()
        }
        "take" -> {
            if (sender.hasPermission(ADMIN_PERMISSION)) {
                Bukkit.getOnlinePlayers().mapNotNull { it.name }
            } else emptyList()
        }
        "transactions" -> {
            if (sender.hasPermission(VIEW_TRANSACTIONS_PERMISSION)) {
                listOf("view")
            } else emptyList()
        }

        else -> emptyList()
    }
}

private suspend fun getThirdLevelSuggestions(sender: Player, firstArg: String, secondArg: String): List<String> {
    return when (firstArg) {
        "changedesign" -> {
            if (sender.hasPermission(CHANGE_DESIGN_PERMISSION)) {
                sender.filterDesigns()
            } else emptyList()
        }
        "transfer" -> {
            if (sender.hasPermission(PAYMENT_PERMISSION)) {
                listOf("[targetCard]")
            } else emptyList()
        }
        "pay" -> {
            if (sender.hasPermission(PAYMENT_PERMISSION)) {
                listOf("[amount]")
            } else emptyList()
        }
        "transactions" -> {
            if (sender.hasPermission(VIEW_TRANSACTIONS_PERMISSION)) {
                Bukkit.getOnlinePlayers().mapNotNull { it.name }
            } else emptyList()
        }
        "subuser" -> {
            if (sender.hasPermission(SUBUSER_PERMISSION)) {
                if (secondArg == "add" || secondArg == "remove") {
                    DBHolder.accountsRepository.getAccountsByName(sender.name)
                        .map { it.id.value.toString() }
                } else emptyList()
            } else emptyList()
        }
        "paywarn" -> {
            if (sender.hasPermission(PAYMENT_PERMISSION)) {
                withContext(ioDispatcher) {
                    (
                    DBHolder.accountsRepository.getAccountsByName(sender.name)
                        .map { it.id.value.toString() }
                            +
                    DBHolder.accountsRepository.getSubuserAccountsByName(sender.name)
                        .map { it.id.value.toString() }
                    ).sorted()
                }
            } else emptyList()
        }

        else -> emptyList()
    }
}

private suspend fun getFourthLevelSuggestions(sender: Player, firstArg: String, secondArg: String, thirdArg: String,): List<String> {
    return when (firstArg) {
        "subuser" -> {
            if (sender.hasPermission(SUBUSER_PERMISSION)) {
                if (secondArg == "remove") {
                    withContext(ioDispatcher) {
                        DBHolder.accountsRepository.getAllSubusersById(thirdArg.toInt())
                    }
                } else {
                    Bukkit.getOnlinePlayers().mapNotNull { it.name }
                }
            } else emptyList()
        }
        "transfer" -> {
            if (sender.hasPermission(PAYMENT_PERMISSION)) {
                listOf("[amount]")
            } else emptyList()
        }
        else -> emptyList()
    }
}