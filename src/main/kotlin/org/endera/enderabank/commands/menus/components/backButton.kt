package org.endera.enderabank.commands.menus.components

import org.bukkit.Material
import org.bukkit.inventory.Inventory
import org.bukkit.inventory.ItemStack
import org.endera.enderabank.configutils.config
import org.endera.enderalib.utils.menu.ItemNameKey
import org.endera.enderalib.utils.menu.createMenuItem

fun Inventory.placeBackButton(
    slot: Int,
    keys: List<ItemNameKey> = listOf()
) {
    this.setItem(
        slot,
        createMenuItem(
            material = Material.getMaterial(config.menu.backButton.item) ?: Material.STONE,
            name = config.menu.backButton.title,
            lore = config.menu.backButton.description,
            modelData = config.menu.backButton.modelData,
            keys = keys
        )
    )
}

fun backButtonItem(): ItemStack {
    return createMenuItem(
        material = Material.getMaterial(config.menu.backButton.item) ?: Material.STONE,
        name = config.menu.backButton.title,
        lore = config.menu.backButton.description,
        modelData = config.menu.backButton.modelData,
    )
}