package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import com.noxcrew.interfaces.properties.interfaceProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Material
import org.bukkit.entity.Player
import org.endera.enderabank.commands.menus.components.pageButtons
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.repository.DBHolder.accountsRepository
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.decodeTransactionType
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.menu.createMenuItem
import org.endera.enderalib.utils.paginate

fun transactionsMenu2(clicker: Player, otherPlayerName: String, initialPage: Int = 0, initialDisplayCoowned: Boolean = false) {
    CoroutineScope(ioDispatcher).launch {
        val transactionsInterface = buildChestInterface {

            val displayCoownedCardsProperty = interfaceProperty(initialDisplayCoowned)
            var displayCoownedCards by displayCoownedCardsProperty

            val pageProperty = interfaceProperty(initialPage)
            var page by pageProperty

            rows = 6
            titleSupplier = { config.menu.transactionsListMenu.title.cparse() }

            withTransform(displayCoownedCardsProperty, pageProperty) { pane, _ ->

                val cards = if (displayCoownedCards) {
                    accountsRepository.getSubuserAccountsByName(otherPlayerName).map { it.id.value }
                } else {
                    accountsRepository.getAccountsByName(otherPlayerName).map { it.id.value }
                }

                val switchButton = if (displayCoownedCards) {
                    createMenuItem(
                        material = Material.getMaterial(config.menu.transactionsListMenu.switchToOwned.item) ?: Material.STONE,
                        name = config.menu.transactionsListMenu.switchToOwned.title,
                        lore = config.menu.transactionsListMenu.switchToOwned.description,
                        modelData = config.menu.transactionsListMenu.switchToOwned.modelData,
                    )
                } else {
                    createMenuItem(
                        material = Material.getMaterial(config.menu.transactionsListMenu.switchToCoowned.item) ?: Material.STONE,
                        name = config.menu.transactionsListMenu.switchToCoowned.title,
                        lore = config.menu.transactionsListMenu.switchToCoowned.description,
                        modelData = config.menu.transactionsListMenu.switchToCoowned.modelData,
                    )
                }

                pane[5, 5] = StaticElement(drawable(switchButton)) {
                    displayCoownedCards = !displayCoownedCards
                    page = 0
                }

                // Add back button
                val backButton = createMenuItem(
                    material = Material.getMaterial(config.menu.backButton.item) ?: Material.STONE,
                    name = config.menu.backButton.title,
                    lore = config.menu.backButton.description,
                    modelData = config.menu.backButton.modelData
                )

                pane[5, 3] = StaticElement(drawable(backButton)) {
                    mainMenu(clicker)
                }

                if (cards.isNotEmpty()) {
                    val cardsTransactions = DBHolder.transactionsRepository.getAllTransactionsByCardIds(cards).reversed().paginate(45)

                    pageButtons(
                        page = page,
                        pane = pane,
                        size = cardsTransactions.size,
                        onPrevious = {
                            page--
                        }, onNext = {
                            page++
                        }
                    )

                    if (cardsTransactions.isNotEmpty() && page < cardsTransactions.size) {
                        cardsTransactions[page].forEachIndexed { index, transaction ->
                            val transactionItem = config.menu.transactionsListMenu.transactionItem
                            pane[index / 9, index % 9] = StaticElement(drawable(
                                createMenuItem(
                                    name = transactionItem.title.replace("{number}", transaction.id.value.toString()),
                                    material = Material.getMaterial(transactionItem.item) ?: Material.STONE,
                                    lore = transactionItem.description.map {
                                        it
                                            .replace("{type}", decodeTransactionType(transaction.transactionType))
                                            .replace("{sender}", transaction.sender)
                                            .replace("{receiver}", transaction.receiver)
                                            .replace("{sender_card}", transaction.senderAccount.toString())
                                            .replace("{receiver_card}", transaction.receiverAccount.toString())
                                            .replace("{amount}", transaction.amount.toString())
                                    },
                                    modelData = transactionItem.modelData
                                )
                            ))
                        }
                    }
                }



            }
        }

        transactionsInterface.open(clicker)
    }
}