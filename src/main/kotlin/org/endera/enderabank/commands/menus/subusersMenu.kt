package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.entity.Player
import org.endera.enderabank.commands.menus.components.backButtonItem
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.AccountEntity
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.stringToArray
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.menu.ItemNameKey
import org.endera.enderalib.utils.menu.createMenuHeadItem

fun subusersMenu(clicker: Player, account: AccountEntity) {
    CoroutineScope(ioDispatcher).launch {
        val id = account.id.toString()
        val subusers = stringToArray(account.subusers)

        val menu = buildChestInterface {
            rows = 5
            titleSupplier = { config.menu.subusersMenu.title.replace("{number}", id).cparse() }

            withTransform { pane, _ ->
                pane[4, 4] = StaticElement(drawable(backButtonItem())) {
                    cardControlMenu(clicker, account.id.value)
                }

                if (subusers.isNotEmpty()) {
                    subusers.forEachIndexed { index, subuserName ->
                        val row = index / 9
                        val col = index % 9

                        val subuserItem = createMenuHeadItem(
                            name = config.menu.subusersMenu.playerItem.title.replace("{nickname}", subuserName),
                            owner = subuserName,
                            lore = config.menu.subusersMenu.playerItem.description,
                        )

                        pane[row, col] = StaticElement(drawable(subuserItem)) {
                            CoroutineScope(ioDispatcher).launch {
                                val accountEntity = DBHolder.accountsRepository.getAccountById(account.id.value)
                                confirmMenu(
                                    clicker = clicker,
                                    confirmMenuScheme = config.menu.confirmDeletionMenu.subuser,
                                    parentView = it.view,
                                    titlePreprocessor = { text -> text.replace("{number}", accountEntity!!.id.toString()) },
                                    onConfirm = { context ->
                                        CoroutineScope(ioDispatcher).launch {
                                            val updatedAccount = DBHolder.accountsRepository.removeSubuser(accountEntity!!.id.value, subuserName)
                                            subusersMenu(clicker, updatedAccount)
                                        }
                                    },
                                    onCancel = { context ->
                                        CoroutineScope(ioDispatcher).launch {
                                            context.view.back()
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }

        menu.open(clicker)
    }
}
