package org.endera.enderabank.commands.menus.components

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.grid.GridPoint
import com.noxcrew.interfaces.pane.Pane
import org.bukkit.Material
import org.endera.enderabank.configutils.config
import org.endera.enderalib.utils.menu.createMenuItem

fun pageButtons(
    page: Int,
    pane: Pane,
    size: Int,
    previousSlot: GridPoint = GridPoint(5, 0),
    nextSlot: GridPoint = GridPoint(5, 8),
    onPrevious: () -> Unit,
    onNext: () -> Unit
) {
    if (page != 0) {
        val previousPageButton = createMenuItem(
            material = Material.getMaterial(config.menu.previousPageItem.item) ?: Material.STONE,
            name = config.menu.previousPageItem.title,
            lore = config.menu.previousPageItem.description,
            modelData = config.menu.previousPageItem.modelData,
        )

        pane[previousSlot] = StaticElement(drawable(previousPageButton)) {
            onPrevious()
        }
    }

    if (page + 1 < size) {
        val nextPageButton = createMenuItem(
            material = Material.getMaterial(config.menu.nextPageItem.item) ?: Material.STONE,
            name = config.menu.nextPageItem.title,
            lore = config.menu.nextPageItem.description,
            modelData = config.menu.nextPageItem.modelData,
        )

        pane[nextSlot] = StaticElement(drawable(nextPageButton)) {
            onNext()
        }
    }
}
