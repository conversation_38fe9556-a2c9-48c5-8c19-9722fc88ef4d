package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import com.noxcrew.interfaces.properties.interfaceProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Material
import org.bukkit.entity.Player
import org.endera.enderabank.commands.menus.components.backButtonItem
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder.accountsRepository
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.menu.createMenuItem

fun cardControlMenu(clicker: Player, accountNumber: Int) {

    CoroutineScope(ioDispatcher).launch {

        val menu = buildChestInterface {
            rows = 4
            titleSupplier = { config.menu.cardActionsMenu.title.replace("{number}", accountNumber.toString()).cparse() }

            val accountProperty = interfaceProperty(accountsRepository.getAccountById(accountNumber)!!)
            var account by accountProperty

            val subusersItem = createMenuItem(
                material = Material.getMaterial(config.menu.cardActionsMenu.subusers.item) ?: Material.STONE,
                name = config.menu.cardActionsMenu.subusers.title,
                lore = config.menu.cardActionsMenu.subusers.description,
                modelData = config.menu.cardActionsMenu.subusers.modelData,
            )

            val deletionItem = createMenuItem(
                material = Material.getMaterial(config.menu.cardActionsMenu.delete.item) ?: Material.STONE,
                name = config.menu.cardActionsMenu.delete.title,
                lore = config.menu.cardActionsMenu.delete.description,
                modelData = config.menu.cardActionsMenu.delete.modelData,
            )


            withTransform { pane, _ ->
                pane[1, 1] = StaticElement(drawable(subusersItem)) {
                    subusersMenu(clicker, account)
                }

                pane[1, 7] = StaticElement(drawable(deletionItem)) {
                    CoroutineScope(ioDispatcher).launch {
                        confirmMenu(
                            clicker = clicker,
                            confirmMenuScheme = config.menu.confirmDeletionMenu.card,
                            titlePreprocessor = { text -> text.replace("{number}", accountNumber.toString()) },
                            parentView = it.view,
                            onConfirm = { context ->
                                CoroutineScope(ioDispatcher).launch {
                                    val accounts = accountsRepository.getAccountsByName(account.playerName)
                                    if (accounts.size > 1 && account.isMain) {
                                        accountsRepository.deleteAccount(accountNumber)
                                        val accounts2 = accountsRepository.getAccountsByName(account.playerName)
                                        accountsRepository.makeAccountMain(accounts2[0].id.value)
                                    } else {
                                        accountsRepository.deleteAccount(accountNumber)
                                    }
                                    cardsMenu(clicker, false)
                                }
                            },
                            onCancel = { context ->
                                CoroutineScope(ioDispatcher).launch {
                                    context.view.back()
                                }
                            }
                        )
                    }
                }

                pane[3, 4] = StaticElement(drawable(backButtonItem())) {
                    cardsMenu(clicker, false)
                }
            }

            withTransform(accountProperty) { pane, _ ->

                if (!account.isMain) {
                    val makeMainItem = createMenuItem(
                        material = Material.getMaterial(config.menu.cardActionsMenu.makeMain.item) ?: Material.STONE,
                        name = config.menu.cardActionsMenu.makeMain.title,
                        lore = config.menu.cardActionsMenu.makeMain.description,
                        modelData = config.menu.cardActionsMenu.makeMain.modelData,
                    )
                    pane[1, 5] = StaticElement(drawable(makeMainItem)) {
                        completingLater = true

                        CoroutineScope(ioDispatcher).launch {
                            account = accountsRepository.makeAccountMain(account.id.value)
                            complete()
                        }
                    }
                }

                val suspendItem = if (!account.isBlocked) {
                    createMenuItem(
                        material = Material.getMaterial(config.menu.cardActionsMenu.lock.item) ?: Material.STONE,
                        name = config.menu.cardActionsMenu.lock.title,
                        lore = config.menu.cardActionsMenu.lock.description,
                        modelData = config.menu.cardActionsMenu.lock.modelData,
                    )
                } else {
                    createMenuItem(
                        material = Material.getMaterial(config.menu.cardActionsMenu.unlock.item) ?: Material.STONE,
                        name = config.menu.cardActionsMenu.unlock.title,
                        lore = config.menu.cardActionsMenu.unlock.description,
                        modelData = config.menu.cardActionsMenu.unlock.modelData,
                    )
                }

                pane[1, 3] = StaticElement(drawable(suspendItem)) {
                    completingLater = true
                    CoroutineScope(ioDispatcher).launch {
                        account = if (!account.isBlocked) {
                            accountsRepository.blockAccount(account.id.value)!!
                        } else {
                            accountsRepository.unblockAccount(account.id.value)!!
                        }
                        complete()
                    }
                }
            }

        }
        menu.open(clicker)

    }
}