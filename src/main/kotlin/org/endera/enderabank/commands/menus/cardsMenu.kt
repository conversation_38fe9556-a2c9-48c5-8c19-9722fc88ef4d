package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import com.noxcrew.interfaces.pane.Pane
import com.noxcrew.interfaces.properties.interfaceProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Material
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack
import org.endera.enderabank.commands.menus.components.backButtonItem
import org.endera.enderabank.commands.menus.components.pageButtons
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder.accountsRepository
import org.endera.enderabank.database.schema.AccountEntity
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.menu.createMenuItem
import org.endera.enderalib.utils.paginate

/**
 * Opens the cards menu interface for the player
 */
fun cardsMenu(player: Player, isDepositMenu: Boolean) {
    CoroutineScope(ioDispatcher).launch {
        val cardsInterface = buildChestInterface {
            rows = 6
            titleSupplier = { config.menu.cardsListMenu.title.cparse() }

            val displayCoownedCardsProperty = interfaceProperty(false)
            var displayCoownedCards by displayCoownedCardsProperty

            val pageProperty = interfaceProperty(0)
            var page by pageProperty

            withTransform(displayCoownedCardsProperty, pageProperty) { pane, _ ->
                val cards = if (displayCoownedCards) {
                    accountsRepository.getSubuserAccountsByName(player.name)
                } else {
                    accountsRepository.getAccountsByName(player.name)
                }.paginate(45)

                if (cards.isNotEmpty()) {
                    populateCardsInPane(pane, player, cards, page, isDepositMenu)
                }

                pane[5, 3] = StaticElement(drawable(backButtonItem())) {
                    mainMenu(it.player)
                }

                pageButtons(
                    page = page,
                    pane = pane,
                    size = cards.size,
                    onPrevious = {
                        page--
                    }, onNext = {
                        page++
                    }
                )

                val switchButton = if (displayCoownedCards) {
                    createMenuItem(
                        material = Material.getMaterial(config.menu.cardsListMenu.switchToOwned.item) ?: Material.STONE,
                        name = config.menu.cardsListMenu.switchToOwned.title,
                        lore = config.menu.cardsListMenu.switchToOwned.description,
                        modelData = config.menu.cardsListMenu.switchToOwned.modelData
                    )
                } else {
                    createMenuItem(
                        material = Material.getMaterial(config.menu.cardsListMenu.switchToCoowned.item) ?: Material.STONE,
                        name = config.menu.cardsListMenu.switchToCoowned.title,
                        lore = config.menu.cardsListMenu.switchToCoowned.description,
                        modelData = config.menu.cardsListMenu.switchToCoowned.modelData
                    )
                }

                pane[5, 5] = StaticElement(drawable(switchButton)) {
                    displayCoownedCards = !displayCoownedCards // TODO() Save page
                    page = 0
                }
            }
        }
        cardsInterface.open(player)
    }
}

/**
 * Populates the pane with card items
 */
private fun populateCardsInPane(
    pane: Pane,
    player: Player,
    cards: List<List<AccountEntity>>,
    page: Int,
    isDepositMenu: Boolean
) {
    cards[page].forEachIndexed { index, accountEntity ->
        val x = index % 9
        val y = index / 9

        val cardItem = createCardItem(player, accountEntity)

        pane[y, x] = StaticElement(drawable(cardItem)) {
            val accountNumber = accountEntity.id.value
            val isOwner = accountEntity.playerName == player.name

            if (!isDepositMenu) {
                if (isOwner) {
                    cardControlMenu(it.player, accountNumber)
                }
            } else {
                CoroutineScope(ioDispatcher).launch {
                    val isCoowner = accountsRepository.getAllSubusersById(accountNumber).contains(player.name)
                    if (isOwner || isCoowner) {
                        depositMenu(player, accountNumber)
                    }
                }
            }
        }
    }
}

/**
 * Creates a card item with all the necessary metadata
 */
private fun createCardItem(player: Player, accountEntity: AccountEntity): ItemStack {
    val designItem = config.designs[accountEntity.design]!!
    val id = accountEntity.id.toString()

    return createMenuItem(
        material = Material.getMaterial(designItem.item) ?: Material.STONE,
        name = config.menu.cardsListMenu.cardItem.cardName.replace("{number}", id),
        lore = config.menu.cardsListMenu.cardItem.description.map {
            it
                .replace("{balance}", accountEntity.balance.toString())
                .replace("{owner}", "${if(accountEntity.playerName == player.name){"<green>"}else{"<red>"} }${accountEntity.playerName}")
                .replace("{is_main}", if (accountEntity.isMain) { config.messages.`true`} else {config.messages.`false`} )
                .replace("{is_blocked}", if (accountEntity.isBlocked) { config.messages.`true`} else {config.messages.`false`} )
        },
        modelData = designItem.modelData,
    )
}