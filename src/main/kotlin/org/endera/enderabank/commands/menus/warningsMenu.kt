package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import com.noxcrew.interfaces.properties.interfaceProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Material
import org.bukkit.entity.Player
import org.endera.enderabank.commands.menus.components.pageButtons
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.decodeWarnStatus
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.menu.createMenuItem
import org.endera.enderalib.utils.paginate

fun warningsMenu(clicker: Player, page: Int, targetPlayer: String? = null) {
    CoroutineScope(ioDispatcher).launch {
        val warningsInterface = buildChestInterface {
            rows = 6
            titleSupplier = { player ->
                config.menu.warningsMenu.listTitle.replace("{player}", player.name).cparse()
            }
            val pageProperty = interfaceProperty(page)
            var currentPage by pageProperty

            withTransform(pageProperty) { pane, _ ->
                val name = targetPlayer ?: clicker.name
                val warnings = DBHolder.warningsRepository.getWarningsByPlayerName(name).reversed().paginate(45)

                val backButton = createMenuItem(
                    material = Material.getMaterial(config.menu.backButton.item) ?: Material.STONE,
                    name = config.menu.backButton.title,
                    lore = config.menu.backButton.description,
                    modelData = config.menu.backButton.modelData
                )

                pane[5, 4] = StaticElement(drawable(backButton)) {
                    mainMenu(clicker)
                }

                if (warnings.isNotEmpty()) {
                    // Add pagination buttons
                    pageButtons(
                        page = currentPage,
                        pane = pane,
                        size = warnings.size,
                        previousSlot = com.noxcrew.interfaces.grid.GridPoint(5, 0),
                        nextSlot = com.noxcrew.interfaces.grid.GridPoint(5, 8),
                        onPrevious = {
                            currentPage--
                        },
                        onNext = {
                            currentPage++
                        }
                    )

                    // Add warning items
                    if (currentPage < warnings.size) {
                        warnings[currentPage].forEachIndexed { index, warningEntity ->
                            val warningItem = config.menu.warningsMenu.warningItem
                            pane[index / 9, index % 9] = StaticElement(drawable(
                                createMenuItem(
                                    name = warningItem.title.replace("{number}", warningEntity.id.value.toString()),
                                    material = Material.getMaterial(warningItem.item) ?: Material.STONE,
                                    modelData = warningItem.modelData,
                                    lore = warningItem.description.map {
                                        it
                                            .replace("{issuer}", warningEntity.issuer)
                                            .replace("{amount}", warningEntity.amount.toString())
                                            .replace("{status}", decodeWarnStatus(warningEntity.state))
                                            .replace("{description}", warningEntity.description)
                                    }
                                )
                            ))
                        }
                    }
                }
            }
        }

        warningsInterface.open(clicker)
    }
}