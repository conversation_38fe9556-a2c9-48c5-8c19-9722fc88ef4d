package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.createMenuItem
import org.endera.enderalib.utils.async.ioDispatcher

fun mainMenu(sender: Player) {

    CoroutineScope(ioDispatcher).launch {

        val yourCardsBalance = DBHolder.accountsRepository.getAccountsByName(sender.name).sumOf { it.balance }
        val otherCardsBalance = DBHolder.accountsRepository.getSubuserAccountsByName(sender.name).sumOf { it.balance }

        val mainMenu = buildChestInterface {
            rows = 3
            titleSupplier = { config.menu.title.cparse() }


            val cardsItem = createMenuItem(
                itemScheme = config.menu.mainMenu.cards,
                descriptionProcessor = {
                    it
                        .replace("{your_balance}", yourCardsBalance.toString())
                        .replace("{others_balance}", otherCardsBalance.toString())
                }
            )

            withTransform { pane, _ ->

                val transactionsItem = createMenuItem(
                    config.menu.mainMenu.transactions
                )

                val warningsItem = createMenuItem(
                    config.menu.mainMenu.warnings
                )

                pane[1,2] = StaticElement(drawable(cardsItem)) {
                    cardsMenu(it.player, false)
                }
                pane[1,4] = StaticElement(drawable(transactionsItem)) {
                    transactionsMenu2(it.player, it.player.name)
                }
                pane[1,6] = StaticElement(drawable(warningsItem)) {
                    warningsMenu(it.player, 0)
                }

            }
        }

        mainMenu.open(sender)
    }

}