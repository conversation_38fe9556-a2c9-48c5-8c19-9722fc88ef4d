//package org.endera.enderabank.commands.menus
//
//import kotlinx.coroutines.CoroutineScope
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//import org.bukkit.Material
//import org.bukkit.entity.Player
//import org.bukkit.event.EventHandler
//import org.bukkit.event.Listener
//import org.bukkit.event.inventory.InventoryClickEvent
//import org.endera.enderabank.bukkitDispatcher
//import org.endera.enderabank.configutils.config
//import org.endera.enderabank.database.repository.DBHolder
//import org.endera.enderabank.database.schema.AccountEntity
//import org.endera.enderabank.utils.CustomGuiFactory
//import org.endera.enderabank.utils.KeyHolder
//import org.endera.enderabank.utils.cparse
//import org.endera.enderalib.utils.async.ioDispatcher
//import org.endera.enderalib.utils.getByKey
//import org.endera.enderalib.utils.menu.ItemNameKey
//import org.endera.enderalib.utils.menu.createMenuItem
//
//fun confirmSubuserDeletion(
//    clicker: Player,
//    account: AccountEntity,
//    subuser: String
//) {
//
//    val id = account.id.toString()
//
//    val customInventory = CustomGuiFactory(
//        type =  CustomGuiFactory.CustomInventoryType.CONFIRM_SUBUSER_DELETION_MENU,
//        size = 27,
//        title = config.menu.confirmDeletionMenu.subuser.title.replace("{number}", id).cparse()
//    )
//
//    val inventory = customInventory.inventory
//
//    inventory.setItem(
//        YES_SLOT,
//        createMenuItem(
//            material = Material.getMaterial(config.menu.confirmDeletionMenu.subuser.confirm.item) ?: Material.STONE,
//            name = config.menu.confirmDeletionMenu.subuser.confirm.title,
//            lore = config.menu.confirmDeletionMenu.subuser.confirm.description,
//            modelData = config.menu.confirmDeletionMenu.subuser.confirm.modelData,
//            keys = listOf(
//                ItemNameKey(
//                    namespacedKey = KeyHolder.cardId,
//                    value = account.id.toString()
//                ),
//                ItemNameKey(
//                    namespacedKey = KeyHolder.nickname,
//                    value = subuser
//                ),
//            )
//        )
//    )
//
//    inventory.setItem(
//        NO_SLOT,
//        createMenuItem(
//            material = Material.getMaterial(config.menu.confirmDeletionMenu.subuser.cancel.item) ?: Material.STONE,
//            name = config.menu.confirmDeletionMenu.subuser.cancel.title,
//            lore = config.menu.confirmDeletionMenu.subuser.cancel.description,
//            modelData = config.menu.confirmDeletionMenu.subuser.cancel.modelData,
//            keys = listOf(
//                ItemNameKey(
//                    namespacedKey = KeyHolder.cardId,
//                    value = account.id.toString()
//                )
//            )
//        )
//    )
//
//    clicker.openInventory(inventory)
//
//}
//
//class SubuserDeletionMenuListener : Listener {
//
//    @EventHandler
//    fun onInventoryClick(event: InventoryClickEvent) {
//
//        event.clickedInventory ?: return
//        if (event.clickedInventory?.holder !is CustomGuiFactory?) return
//        val holder = event.clickedInventory?.holder as CustomGuiFactory?
//        if (holder?.type != CustomGuiFactory.CustomInventoryType.CONFIRM_SUBUSER_DELETION_MENU) return
//
//        event.isCancelled = true
//
//        val clicker = event.whoClicked as? Player ?: return
//
//        val clickedItem = event.currentItem ?: return
//        val meta = clickedItem.itemMeta ?: return
//
//        when (event.slot) {
//            YES_SLOT -> {
//                CoroutineScope(ioDispatcher).launch {
//                    val id = meta.getByKey(KeyHolder.cardId)!!.toInt()
//                    val subuser = meta.getByKey(KeyHolder.nickname)!!
//                    val account = DBHolder.accountsRepository.removeSubuser(id, subuser)
//
//                    withContext(bukkitDispatcher) {
//                        subusersMenu(clicker, account)
//                    }
//                }
//            }
//            NO_SLOT -> {
//                CoroutineScope(ioDispatcher).launch {
//                    val id = meta.getByKey(KeyHolder.cardId)!!.toInt()
//                    val account = DBHolder.accountsRepository.getAccountById(id)
//                    withContext(bukkitDispatcher) {
//                        subusersMenu(clicker, account!!)
//                    }
//                }
//            }
//        }
//
//    }
//}