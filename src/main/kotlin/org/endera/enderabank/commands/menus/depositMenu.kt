package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import com.noxcrew.interfaces.properties.InterfaceProperty
import com.noxcrew.interfaces.properties.interfaceProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.bukkit.Material
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack
import org.endera.enderabank.bukkitDispatcher
import org.endera.enderabank.commands.menus.components.backButtonItem
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.AccountEntity
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.withdrawDoubleCurrency
import org.endera.enderabank.utils.withdrawSingleCurrency
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.menu.createMenuItem


/**
 * Opens the deposit menu interface for the player
 */
fun depositMenu(player: Player, accountId: Int) {
    CoroutineScope(ioDispatcher).launch {
        val depositInterface = buildChestInterface {
            rows = 5
            titleSupplier = { config.menu.depositMenu.title.replace("{number}", accountId.toString()).cparse() }

            val accountProperty = interfaceProperty(DBHolder.accountsRepository.getAccountById(accountId)!!)
            var account by accountProperty

            withTransform(accountProperty) { pane, _ ->
                // Balance display
                val balanceItem = createMenuItem(
                    name = config.menu.depositMenu.balanceItem.title.currencyName()
                        .replace("{balance}", account.balance.toString()),
                    modelData = config.menu.depositMenu.balanceItem.modelData,
                    lore = config.menu.depositMenu.balanceItem.description,
                    material = Material.getMaterial(config.menu.depositMenu.balanceItem.item) ?: Material.STONE
                )
                pane[0, 4] = StaticElement(drawable(balanceItem))

                // Deposit buttons
                val deposit1Item = createMenuItem(
                    name = config.menu.depositMenu.deposit1.title.currencyName(),
                    material = Material.getMaterial(config.menu.depositMenu.deposit1.item) ?: Material.STONE,
                    modelData = config.menu.depositMenu.deposit1.modelData,
                    lore = config.menu.depositMenu.deposit1.description
                )
                pane[2, 1] = StaticElement(drawable(deposit1Item)) {
                    depositOre(it.player, 1, accountProperty)
                }

                val deposit10Item = createMenuItem(
                    name = config.menu.depositMenu.deposit10.title.currencyName(),
                    material = Material.getMaterial(config.menu.depositMenu.deposit10.item) ?: Material.STONE,
                    modelData = config.menu.depositMenu.deposit10.modelData,
                    lore = config.menu.depositMenu.deposit10.description
                )
                pane[2, 2] = StaticElement(drawable(deposit10Item)) {
                    depositOre(it.player, 10, accountProperty)
                }

                val deposit64Item = createMenuItem(
                    name = config.menu.depositMenu.deposit64.title.currencyName(),
                    material = Material.getMaterial(config.menu.depositMenu.deposit64.item) ?: Material.STONE,
                    modelData = config.menu.depositMenu.deposit64.modelData,
                    lore = config.menu.depositMenu.deposit64.description
                )
                pane[2, 3] = StaticElement(drawable(deposit64Item)) {
                    depositOre(it.player, 64, accountProperty)
                }

                val withdraw1Item = createMenuItem(
                    name = config.menu.depositMenu.withdraw1.title.currencyName(),
                    material = Material.getMaterial(config.menu.depositMenu.withdraw1.item) ?: Material.STONE,
                    modelData = config.menu.depositMenu.withdraw1.modelData,
                    lore = config.menu.depositMenu.withdraw1.description
                )
                pane[2, 5] = StaticElement(drawable(withdraw1Item)) {
                    withdrawCurrency(it.player, 1, accountProperty)
                }

                val withdraw10Item = createMenuItem(
                    name = config.menu.depositMenu.withdraw10.title.currencyName(),
                    material = Material.getMaterial(config.menu.depositMenu.withdraw10.item) ?: Material.STONE,
                    modelData = config.menu.depositMenu.withdraw10.modelData,
                    lore = config.menu.depositMenu.withdraw10.description
                )
                pane[2, 6] = StaticElement(drawable(withdraw10Item)) {
                    withdrawCurrency(it.player, 10, accountProperty)
                }

                val withdraw64Item = createMenuItem(
                    name = config.menu.depositMenu.withdraw64.title.currencyName(),
                    material = Material.getMaterial(config.menu.depositMenu.withdraw64.item) ?: Material.STONE,
                    modelData = config.menu.depositMenu.withdraw64.modelData,
                    lore = config.menu.depositMenu.withdraw64.description
                )
                pane[2, 7] = StaticElement(drawable(withdraw64Item)) {
                    withdrawCurrency(it.player, 64, accountProperty)
                }

                // Back button
                pane[4, 4] = StaticElement(drawable(backButtonItem())) {
                    cardsMenu(it.player, true)
                }
            }
        }
        depositInterface.open(player)
    }
}

/**
 * Deposits currency into the account
 */
private fun depositOre(player: Player, amount: Int, accountProperty: InterfaceProperty<AccountEntity>) {
    var account by accountProperty

    val withdrawResult = if (config.currencySettings.doubleCurrencyMode) {
        withdrawDoubleCurrency(player, amount)
    } else {
        withdrawSingleCurrency(player, amount)
    }

    if (withdrawResult) {
        CoroutineScope(ioDispatcher).launch {
            account = DBHolder.accountsRepository.addBalance(account.id.value, amount)!!
        }
    } else {
        player.sendMessage(config.messages.errorMessages.notEnoughItems.cparse())
    }
}

private val accountMutexes = mutableMapOf<Int, Mutex>()

private fun getAccountMutex(accountId: Int): Mutex {
    return accountMutexes.getOrPut(accountId) { Mutex() }
}

/**
 * Withdraws currency from the account
 */
private fun withdrawCurrency(player: Player, amount: Int, accountProperty: InterfaceProperty<AccountEntity>) {
    CoroutineScope(ioDispatcher).launch {
        var account by accountProperty
        val accountMutex = getAccountMutex(account.id.value)
        accountMutex.withLock {
            val firstCurrency = Material.getMaterial(config.currencySettings.firstCurrency) ?: Material.DIAMOND_ORE
            val secondCurrency = Material.getMaterial(config.currencySettings.secondCurrency) ?: Material.DEEPSLATE_DIAMOND_ORE

            if (account.balance >= amount) {
                val inventory = player.inventory
                val armors = inventory.armorContents.filterNotNull().size
                val leftHand = if (inventory.itemInOffHand.isEmpty) 0 else 1
                if ((inventory.contents.filterNotNull().size - armors - leftHand) < 36) {
                    account = DBHolder.accountsRepository.removeBalance(account.id.value, amount)!!
                    if (config.currencySettings.doubleCurrencyMode) {
                        val itemStack = ItemStack(secondCurrency, amount)
                        launch(bukkitDispatcher) {
                            inventory.addItem(itemStack)
                        }
                    } else {
                        val itemStack = ItemStack(firstCurrency, amount)
                        launch(bukkitDispatcher) {
                            inventory.addItem(itemStack)
                        }
                    }
                } else {
                    player.sendMessage(config.messages.errorMessages.notEnoughSlots.cparse())
                }
            } else {
                player.sendMessage(config.messages.errorMessages.insufficientFunds.cparse())
            }
        }
    }
}

/**
 * Extension function to replace currency name placeholder
 */
private fun String.currencyName(): String {
    return this.replace("{currency_name}", config.currencySettings.currencyName)
}