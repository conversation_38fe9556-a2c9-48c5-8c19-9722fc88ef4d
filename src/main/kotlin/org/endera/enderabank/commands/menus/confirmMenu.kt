package org.endera.enderabank.commands.menus

import com.noxcrew.interfaces.click.ClickContext
import com.noxcrew.interfaces.drawable.Drawable.Companion.drawable
import com.noxcrew.interfaces.element.StaticElement
import com.noxcrew.interfaces.interfaces.buildChestInterface
import com.noxcrew.interfaces.view.InterfaceView
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.ConfirmItemDeletion
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.createMenuItem

suspend fun confirmMenu(
    clicker: Player,
    confirmMenuScheme: ConfirmItemDeletion,
    parentView: InterfaceView? = null,
    titlePreprocessor: (String) -> String = { it },
    onConfirm: (context: ClickContext) -> Unit,
    onCancel: (context: ClickContext) -> Unit,
) {
    val confirmInterface = buildChestInterface {
        rows = 3
        titleSupplier = { titlePreprocessor(confirmMenuScheme.title).cparse() }

        val cancelItem = createMenuItem(confirmMenuScheme.cancel)
        val confirmItem = createMenuItem(confirmMenuScheme.confirm)

        withTransform { pane, _ ->
            pane[1, 3] = StaticElement(drawable(confirmItem)) {
                onConfirm(it)
            }

            pane[1, 5] = StaticElement(drawable(cancelItem)) {
                onCancel(it)
            }
        }
    }

    confirmInterface.open(clicker, parentView)

}