package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

fun unwarnCommand(sender: Player, args: Array<out String>) {

    if (args.size != 2) {
        sender.sendMessage(config.messages.commandsUsage.unwarnCommand.cparse())
        return
    }

    CoroutineScope(ioDispatcher).launch {
        val warn = DBHolder.warningsRepository.getWarningById(args[1].toInt())
        if (warn == null) {
            sender.sendMessage(config.messages.errorMessages.warnDoesNotExist.cparse())
            return@launch
        }
        DBHolder.warningsRepository.deleteWarning(warn.id.value)
        sender.sendMessage(config.messages.warnDeleted.replace("{number}", warn.id.value.toString()).cparse())
    }


}