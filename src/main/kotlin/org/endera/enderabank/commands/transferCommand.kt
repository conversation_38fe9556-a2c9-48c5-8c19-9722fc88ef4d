package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.endera.enderabank.bukkitDispatcher
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.TransactionType
import org.endera.enderabank.utils.calcCommission
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.withdrawCommission
import org.endera.enderalib.utils.async.ioDispatcher

fun transferCommand(sender: Player, args: Array<out String>) {

    if (args.size != 4) {
        sender.sendMessage(config.messages.commandsUsage.transferCommand.cparse())
        return
    }
    CoroutineScope(ioDispatcher).launch {
        try {
            val accsRepo = DBHolder.accountsRepository
            val receiverAcc = accsRepo.getAccountById(args[2].toInt())
            if (receiverAcc == null) {
                sender.sendMessage(config.messages.errorMessages.cardDoesNotExist.cparse())
                return@launch
            }
            if (receiverAcc.isBlocked) {
                sender.sendMessage(config.messages.errorMessages.receiverCardIsBlocked.cparse())
                return@launch
            }
            val senderAcc = accsRepo.getAccountById(args[1].toInt())
            if (senderAcc == null) {
                sender.sendMessage(config.messages.errorMessages.cardDoesNotExist.cparse())
                return@launch
            }
            if (senderAcc.playerName != sender.name && !senderAcc.subusers.contains(sender.name)) {
                sender.sendMessage(config.messages.errorMessages.noSuchCard.cparse())
                return@launch
            }
            if (senderAcc.isBlocked) {
                sender.sendMessage(config.messages.errorMessages.senderCardIsBlocked.cparse())
                return@launch
            }
            val amount = args[3].toInt()
            if (amount < 1) {
                sender.sendMessage(config.messages.errorMessages.minimalAmount.cparse())
                return@launch
            }
            if (amount + calcCommission(sender, senderAcc.id.value) <= senderAcc.balance) {
                withdrawCommission(sender, senderAcc.id.value)
                accsRepo.removeBalance(senderAcc.id.value, amount)
                accsRepo.addBalance(receiverAcc.id.value, amount)
                DBHolder.transactionsRepository.addTransaction(
                    amount = amount,
                    transactionType = TransactionType.TRANSFER,
                    sender = sender.name,
                    receiver = receiverAcc.playerName,
                    senderAccount = senderAcc.id.value,
                    receiverAccount = receiverAcc.id.value
                )
                sender.sendMessage(
                    config.messages.transferSent
                        .replace("{amount}", amount.toString())
                        .replace("{account}", receiverAcc.id.value.toString())
                        .cparse()
                )

                val receiver = Bukkit.getOfflinePlayer(receiverAcc.playerName)
                if (receiver.isOnline) {
                    receiver.player!!.sendMessage(
                        config.messages.receivedPayment
                            .replace("{amount}", amount.toString())
                            .replace("{sender}", sender.name)
                            .replace("{account}", receiverAcc.id.value.toString())
                            .cparse()
                    )
                }
            } else {
                sender.sendMessage(config.messages.errorMessages.insufficientFunds.cparse())
            }

        } catch (e: Exception) {
            withContext(bukkitDispatcher) {
                println("Ошибка очка")
            }
            e.printStackTrace()
        }
    }

}