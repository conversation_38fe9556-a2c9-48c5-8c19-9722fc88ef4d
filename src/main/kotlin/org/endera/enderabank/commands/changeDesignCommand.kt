package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse

fun changeDesignCommand(sender: Player, args: Array<out String>) {

    if (args.size < 3) {
        sender.sendMessage(config.messages.commandsUsage.changeDesign.cparse())
        return
    }

    val design = args[2]
    val cardId = args[1].toInt()
    if (design !in config.designs.keys) {
        sender.sendMessage(config.messages.errorMessages.invalidDesign.cparse())
        return
    }

    if (!sender.hasPermission("enderabank.design.$design")) {
        sender.sendMessage(config.messages.errorMessages.noPermissionForDesign.cparse())
        return
    }

    CoroutineScope(Dispatchers.IO).launch {
        val card = DBHolder.accountsRepository.getAccountById(cardId)

        if (card == null || card.playerName != sender.name) {
            sender.sendMessage(config.messages.errorMessages.noSuchCard.cparse())
            return@launch
        }

        DBHolder.accountsRepository.changeDesign(cardId, design)
        sender.sendMessage(config.messages.designChanged.cparse())

    }

}