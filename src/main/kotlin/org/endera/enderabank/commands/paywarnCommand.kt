package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.TransactionType
import org.endera.enderabank.database.schema.WarningState
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

fun paywarnCommand(sender: Player, args: Array<out String>) {

    if (args.size != 3) {
        sender.sendMessage(config.messages.commandsUsage.paywarnCommand.cparse())
        return
    }

    CoroutineScope(ioDispatcher).launch {
        val account = DBHolder.accountsRepository.getAccountById(args[2].toInt())
        if (account == null || (account.playerName != sender.name && !account.subusers.split(",").contains(sender.name))) {
            sender.sendMessage(config.messages.errorMessages.noSuchCard.cparse())
            return@launch
        }
        val warn = DBHolder.warningsRepository.getWarningById(args[1].toInt())
        if (warn == null) {
            sender.sendMessage(config.messages.errorMessages.warnDoesNotExist.cparse())
            return@launch
        }

        if ( (warn.playerName != sender.name) and !config.tweaks.allowPayingOthersWarns) {
            sender.sendMessage(config.messages.errorMessages.cantPayOthersWarn.cparse())
            return@launch
        }

        if (warn.state == WarningState.PAID) {
            sender.sendMessage(config.messages.errorMessages.warnIsPaid.cparse())
            return@launch
        }
        if (account.balance >= warn.amount) {
            DBHolder.accountsRepository.removeBalance(account.id.value, warn.amount)
            DBHolder.accountsRepository.addBalance(config.governmentCard, warn.amount)
            DBHolder.warningsRepository.setWarningState(warn.id.value, WarningState.PAID)
            DBHolder.transactionsRepository.addTransaction(
                amount = warn.amount,
                transactionType = TransactionType.PENALTY,
                sender = sender.name,
                receiver = "Goverment",
                senderAccount = account.id.value,
                receiverAccount = config.governmentCard
            )
            sender.sendMessage(config.messages.warnPaid.replace("{number}", warn.id.value.toString()).cparse())
        }
    }

}