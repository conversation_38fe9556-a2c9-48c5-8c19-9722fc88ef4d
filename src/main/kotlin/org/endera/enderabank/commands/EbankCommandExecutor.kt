package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.entity.Player
import org.endera.enderabank.commands.Perms.ADMIN_PERMISSION
import org.endera.enderabank.commands.Perms.CHANGE_DESIGN_PERMISSION
import org.endera.enderabank.commands.Perms.DEPOSIT_PERMISSION
import org.endera.enderabank.commands.Perms.FBI_PERMISSION
import org.endera.enderabank.commands.Perms.NEWCARD_PERMISSION
import org.endera.enderabank.commands.Perms.PAYMENT_PERMISSION
import org.endera.enderabank.commands.Perms.RELOAD_PERMISSION
import org.endera.enderabank.commands.Perms.SUBUSER_PERMISSION
import org.endera.enderabank.commands.Perms.VIEW_TRANSACTIONS_PERMISSION
import org.endera.enderabank.commands.menus.cardsMenu
import org.endera.enderabank.commands.menus.mainMenu
import org.endera.enderabank.commands.subuser.handleSubuserCommand
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.warns
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.checkPermission

class EbankCommandExecutor : CommandExecutor {

    override fun onCommand(sender: CommandSender, command: Command, label: String, args: Array<out String>): Boolean {

        if (args.isNotEmpty()) {
            when (args[0]) {
                "reload" -> {
                    sender.checkPermission(RELOAD_PERMISSION) {
                        reloadConfigCommand(sender)
                    }
                    return true
                }
                "take" -> {
                    sender.checkPermission(ADMIN_PERMISSION) {
                        takeCommand(sender, args)
                    }
                    return true
                }
            }
        }

        if (sender !is Player) {
            sender.sendMessage("Only players can use this command!")
            return true
        }

        if (args.isEmpty()) return mainMenu(sender).let { true }

        when (args[0]) {
            "newcard" -> {
                sender.checkPermission(NEWCARD_PERMISSION) {
                    newCardCommand(sender, args)
                }
            }
            "changedesign" -> {
                sender.checkPermission(CHANGE_DESIGN_PERMISSION) {
                    changeDesignCommand(sender, args)
                }
            }
            "subuser" -> {
                sender.checkPermission(SUBUSER_PERMISSION) {
                    handleSubuserCommand(sender, args)
                }
            }
            "pay" -> {
                // TODO() Implement real permission
                sender.checkPermission(PAYMENT_PERMISSION) {
                    payCommand(sender, args)
                }
            }
            "transfer" -> {
                sender.checkPermission(PAYMENT_PERMISSION) {
                    transferCommand(sender, args)
                }
            }
            "dgive" -> {
                sender.checkPermission("enderabank.dgive") {
                    CoroutineScope(ioDispatcher).launch {
                        val rep = DBHolder.accountsRepository
                        val acc = rep.getMainAccountByName(sender.name)!!
                        rep.addBalance(acc.id.value, args[1].toInt())
                        sender.sendMessage("Саккес")
                    }
                }
            }
            "warn" -> {
                sender.checkPermission(FBI_PERMISSION) {
                    warnCommand(sender, args)
                }
            }
            "unwarn" -> {
                sender.checkPermission(FBI_PERMISSION) {
                    unwarnCommand(sender, args)
                }
            }
            "paywarn" -> {
                sender.checkPermission(PAYMENT_PERMISSION) {
                    paywarnCommand(sender, args)
                }
            }
            "warns" -> {
                sender.checkPermission(FBI_PERMISSION) {
                    warns(sender, args)
                }
            }
            "deposit" -> {
                sender.checkPermission(DEPOSIT_PERMISSION) {
                    cardsMenu(sender, true)
                }
            }
            "transactions" -> {
                sender.checkPermission(VIEW_TRANSACTIONS_PERMISSION) {
                    transactionCommands(sender, args)
                }
            }

            else -> {
                sender.sendMessage(config.messages.errorMessages.noCommand.cparse())
            }

        }

        return true
    }

}