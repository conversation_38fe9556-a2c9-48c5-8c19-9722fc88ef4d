package org.endera.enderabank.commands

import org.bukkit.command.CommandSender
import org.bukkit.entity.Player
import org.endera.enderabank.configManager
import org.endera.enderabank.configutils.config
import org.endera.enderabank.rlogger
import org.endera.enderabank.utils.cparse

fun reloadConfigCommand(sender: CommandSender) {

    try {
        val loadedConfig = configManager.loadOrCreateConfig()
        config = loadedConfig
        sender.sendMessage(config.messages.configReloaded.cparse())

    } catch (e: Exception) {
        rlogger.severe(e.message)
        if (sender is Player) {
            sender.sendMessage("<red>Ошибка загрузки конфигурации!")
        }
    }
}