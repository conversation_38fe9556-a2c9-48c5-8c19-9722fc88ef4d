package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.TransactionType
import org.endera.enderabank.utils.calcCommission
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.utils.withdrawCommission
import org.endera.enderalib.utils.async.ioDispatcher

fun payCommand(sender: Player, args: Array<out String>) {

    if (args.size != 3) {
        sender.sendMessage(config.messages.commandsUsage.payCommand.cparse())
        return
    }
    CoroutineScope(ioDispatcher).launch {

        val receiver = args[1]

        if (!Bukkit.getOfflinePlayer(receiver).hasPlayedBefore()) {
            sender.sendMessage(config.messages.errorMessages.noSuchPlayerPlayed.cparse())
            return@launch
        }
        val accsRepo = DBHolder.accountsRepository
        val receiverAcc = accsRepo.getMainAccountByName(receiver)
        if (receiverAcc == null) {
            sender.sendMessage(config.messages.errorMessages.receiverHasNoCards.cparse())
            return@launch
        }
        if (receiverAcc.isBlocked) {
            sender.sendMessage(config.messages.errorMessages.receiverCardIsBlocked.cparse())
            return@launch
        }
        val senderAcc = accsRepo.getMainAccountByName(sender.name)
        if (senderAcc == null) {
            sender.sendMessage(config.messages.errorMessages.senderHasNoCards.cparse())
            return@launch
        }
        if (senderAcc.isBlocked) {
            sender.sendMessage(config.messages.errorMessages.senderCardIsBlocked.cparse())
            return@launch
        }
        val amount = args[2].toInt()
        if (amount < 1) {
            sender.sendMessage(config.messages.errorMessages.minimalAmount.cparse())
            return@launch
        }
        if (amount+calcCommission(sender, senderAcc.id.value) <= senderAcc.balance) {
            withdrawCommission(sender)
            accsRepo.removeBalance(senderAcc.id.value, amount)
            accsRepo.addBalance(receiverAcc.id.value, amount)
            DBHolder.transactionsRepository.addTransaction(
                amount = amount,
                transactionType = TransactionType.TRANSFER,
                sender = sender.name,
                receiver = receiver,
                senderAccount = senderAcc.id.value,
                receiverAccount = receiverAcc.id.value
            )
            sender.sendMessage(
                config.messages.paymentSent
                    .replace("{amount}", amount.toString())
                    .replace("{player}", receiver)
                    .cparse()
            )
            val receiverPlayer = Bukkit.getOfflinePlayer(receiverAcc.playerName)
            if (receiverPlayer.isOnline) {
                receiverPlayer.player!!.sendMessage(
                    config.messages.receivedPayment
                        .replace("{amount}", amount.toString())
                        .replace("{sender}", sender.name)
                        .replace("{account}", receiverAcc.id.value.toString())
                        .cparse()
                )
            }
        } else {
            sender.sendMessage(config.messages.errorMessages.insufficientFunds.cparse())
        }

    }

}