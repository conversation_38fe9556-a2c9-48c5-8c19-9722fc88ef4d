package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.command.CommandSender
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

fun takeCommand(sender: CommandSender, args: Array<out String>) {

    if (args.size != 3) {
        sender.sendMessage(config.messages.commandsUsage.takeCommand.cparse())
        return
    }

    val targetOfflinePlayer = sender.server.getOfflinePlayer(args[1])

    if (!targetOfflinePlayer.hasPlayedBefore()) {
        sender.sendMessage(config.messages.errorMessages.noSuchPlayerPlayed.cparse())
        return
    }
    val targetPlayer = targetOfflinePlayer.player!!

    CoroutineScope(ioDispatcher).launch {
        val repo = DBHolder.accountsRepository
        val amount = args[2].toInt()
        val account = repo.getMainAccountByName(targetPlayer.name)
        if (account == null) {
            sender.sendMessage(config.messages.errorMessages.noSuchCard.cparse())
            return@launch
        }
        if (account.balance < amount) {
            sender.sendMessage(config.messages.errorMessages.notEnoughMoneyToTake.cparse())
            return@launch
        }
        repo.removeBalance(account.id.value, amount)
        repo.addBalance(config.governmentCard, amount)
        targetPlayer.sendMessage(config.messages.moneyTaken.replace("{amount}", amount.toString()).cparse())
        sender.sendMessage(config.messages.successfullyTakenMoney.replace("{player}", targetPlayer.name).cparse())
    }

}