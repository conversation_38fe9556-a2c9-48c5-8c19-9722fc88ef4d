package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cardsLimitCounter
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

fun newCardCommand(sender: Player, args: Array<out String>) {
    if (args.size < 2) {
        sender.sendMessage(config.messages.commandsUsage.newcard.cparse())
        return
    }
    if (args[1] !in config.designs.keys) {
        sender.sendMessage(config.messages.errorMessages.invalidDesign.cparse())
        return
    }

    if (!sender.hasPermission("enderabank.design.${args[1]}")) {
        sender.sendMessage(config.messages.errorMessages.noPermissionForDesign.cparse())
        return
    }

    CoroutineScope(ioDispatcher).launch {
        val account = DBHolder.accountsRepository.getAccountsByName(sender.name)
        if (account.size < cardsLimitCounter(sender)) {
            val createdCard = DBHolder.accountsRepository.createAccount(
                playerName = sender.name,
                design = args[1],
                isMain = account.isEmpty()
            )
            sender.sendMessage(config.messages.cardCreated.replace("{number}", createdCard.id.toString()).cparse())
        } else {
            sender.sendMessage(config.messages.errorMessages.cardsLimitReached.cparse())
        }
    }
}
