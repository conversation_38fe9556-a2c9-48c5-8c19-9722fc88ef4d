package org.endera.enderabank.commands

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

fun warnCommand(sender: Player, args: Array<out String>) {

    @Suppress("UNUSED_VARIABLE")
    val ownss = "wewnew: https://black-minecraft.com/members/suilte.79297/"

    if (args.size < 3) {
        sender.sendMessage(config.messages.commandsUsage.warnCommand.cparse())
        return
    }

    val issuer = sender.name
    val receiver = args[1]
    val amount = args[2].toInt()
    val description = args.slice(3 until args.size).joinToString(" ")

    val receiverPlayer = Bukkit.getOfflinePlayer(receiver)
    if (!receiverPlayer.hasPlayedBefore()) {
        sender.sendMessage(config.messages.errorMessages.noSuchPlayerPlayed.cparse())
        return
    }

    CoroutineScope(ioDispatcher).launch {
        val warn = DBHolder.warningsRepository.createWarning(
            playerName = receiver,
            issuer = issuer,
            amount = amount,
            description = description,
        )
        sender.sendMessage(
            config.messages.warnCreated
                .replace("{number}", warn.id.value.toString())
                .replace("{player}", receiver)
                .cparse()
        )
        if (receiverPlayer.isOnline) {
            receiverPlayer.player!!.sendMessage(
                config.messages.warnReceived
                    .replace("{number}", warn.id.value.toString())
                    .replace("{player}", issuer)
                    .replace("{amount}", amount.toString())
                    .replace("{description}", description)
                    .cparse()
            )
        }
    }




}