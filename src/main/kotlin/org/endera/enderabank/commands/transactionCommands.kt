package org.endera.enderabank.commands

import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.endera.enderabank.commands.menus.transactionsMenu2
import org.endera.enderabank.configutils.config
import org.endera.enderabank.utils.cparse

fun transactionCommands(sender: Player, args: Array<out String>) {

    if (args.size < 3 || args[1] != "view") {
        sender.sendMessage(config.messages.commandsUsage.viewTransactions.cparse())
        return
    }
    val target = Bukkit.getOfflinePlayer(args[2])
    if (!target.hasPlayedBefore()) {
        sender.sendMessage(config.messages.errorMessages.noSuchPlayerPlayed.cparse())
        return
    }

    transactionsMenu2(sender, target.name!!)

}