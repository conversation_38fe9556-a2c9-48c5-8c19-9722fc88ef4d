package org.endera.enderabank.commands.subuser

import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.utils.cparse

fun handleSubuserCommand(sender: Player, args: Array<out String>) {

    if (args.size < 2) {
        sender.sendMessage(config.messages.errorMessages.noCommand.cparse())
        return
    }

    when (args[1]) {
        "add" -> {
            addSubuserCommand(sender, args)
        }
        "remove" -> {
            removeSubuserCommand(sender, args)
        }
        else -> {
            sender.sendMessage(config.messages.errorMessages.noCommand.cparse())
        }

    }

}