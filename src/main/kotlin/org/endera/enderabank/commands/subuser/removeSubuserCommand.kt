package org.endera.enderabank.commands.subuser

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

fun removeSubuserCommand(sender: Player, args: Array<out String>) {

    if (args.size < 4) {
        sender.sendMessage(config.messages.commandsUsage.removeSubuser.cparse())
        return
    }

    CoroutineScope(ioDispatcher).launch {
        val account = DBHolder.accountsRepository.getAccountsByName(sender.name).firstOrNull { it.id.value == args[2].toInt() }

        if (account == null) {
            sender.sendMessage(config.messages.errorMessages.noSuchCard.cparse())
            return@launch
        }
        if (account.playerName != sender.name) {
            sender.sendMessage(config.messages.errorMessages.noSuchCard.cparse())
            return@launch
        }
        val id = account.id.value

        val subuser = Bukkit.getOfflinePlayer(args[3])
        if (!subuser.hasPlayedBefore()) {
            sender.sendMessage(config.messages.errorMessages.noSuchPlayerPlayed.cparse())
            return@launch
        }

        if (subuser.name !in DBHolder.accountsRepository.getAllSubusersById(id)) {
            sender.sendMessage(config.messages.errorMessages.subuserIsNotExists.cparse())
            return@launch
        }

        DBHolder.accountsRepository.removeSubuser(id, subuser.name!!)
        sender.sendMessage(config.messages.subuserRemoved
            .replace("{nickname}", subuser.name!!)
            .replace("{account}", id.toString())
            .cparse()
        )
    }


}