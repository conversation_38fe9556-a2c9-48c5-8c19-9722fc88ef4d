package org.endera.enderabank.placeholderapi

import kotlinx.coroutines.runBlocking
import me.clip.placeholderapi.expansion.PlaceholderExpansion
import org.bukkit.entity.Player
import org.endera.enderabank.EnderaBank
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder

class EnderaBankExpansion(private val plugin: EnderaBank) : PlaceholderExpansion() {

    override fun getIdentifier(): String {
        return "enderabank"
    }

    @Suppress("DEPRECATION")
    override fun getAuthor(): String {
        return plugin.description.authors.joinToString(", ")

    }

    @Suppress("DEPRECATION")
    override fun getVersion(): String {
        return plugin.description.version
    }

    override fun persist(): Boolean {
        return true
    }

    override fun onPlaceholderRequest(player: Player?, identifier: String): String? {
        if (player == null) {
            return "Player doesn't exist"
        }

        return runBlocking {
            val repo = DBHolder.accountsRepository
            val account = repo.getMainAccountByName(player.name) ?: return@runBlocking null
            val allAccounts = repo.getAccountsByName(player.name).sumOf { it.balance }
            val allSubuserAccounts = repo.getSubuserAccountsByName(player.name).sumOf { it.balance }

            when (identifier) {
                "balance" -> allAccounts.toString()
                "balance_formatted" -> allAccounts.toString() + config.currencySettings.currencyName
                "balance_main" -> account.balance.toString()
                "balance_main_formatted" -> account.balance.toString() + config.currencySettings.currencyName
                "balance_others" -> allSubuserAccounts.toString()
                "balance_others_formatted" -> allSubuserAccounts.toString() + config.currencySettings.currencyName
                else -> null
            }
        }


    }

}