package org.endera.enderabank.notifications

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.player.PlayerJoinEvent
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.database.schema.WarningState
import org.endera.enderabank.utils.cparse
import org.endera.enderalib.utils.async.ioDispatcher

class WarnsNotification : Listener {

    @EventHandler
    fun onPlayerJoin(event: PlayerJoinEvent){

        if (!config.tweaks.warnsNotificationEnabled) return

        CoroutineScope(ioDispatcher).launch {

            val player = event.player
            val warnings = DBHolder.warningsRepository.getWarningsByPlayerName(player.name).filter { it.state == WarningState.PENDING }

            if (warnings.isNotEmpty()) {
                player.sendMessage(
                    config.messages.warnsUnpaid
                        .replace("{number}", warnings.size.toString())
                        .replace("{amount}", warnings.sumOf { it.amount }.toString() )
                        .cparse()
                )
            }

        }
    }
}