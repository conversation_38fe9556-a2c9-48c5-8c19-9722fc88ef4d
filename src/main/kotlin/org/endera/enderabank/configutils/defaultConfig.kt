package org.endera.enderabank.configutils

val defaultConfig = ConfigScheme(
    storage = DataStorage(
        storageType = "h2",
        remote = Remote(
            host = "localhost",
            port = 3306,
            dbname = "yourdbname",
            user = "youruser",
            password = "yourpassword",
            connectionParameters = "?autoReconnect=true&initialTimeout=1&useSSL=false"
        ),
    ),
    governmentCard = 1,
    commissionAmount = 1,
    currencySettings = CurrencySettings(
        currencyName = "АР",
        doubleCurrencyMode = true,
        firstCurrency = "DIAMOND_ORE",
        secondCurrency = "DEEPSLATE_DIAMOND_ORE",
    ),
    tweaks = Tweaks(
        warnsNotificationEnabled = true,
        allowPayingOthersWarns = true,
    ),
    prefix = "<gradient:#5e4fa2:#f79459>[EnderaBank]</gradient>",
    cardLimits = CardLimits(
        default = 5,
        limits = mapOf(
            "plus" to 7,
            "premium" to 9
        )
    ),
    designs = mapOf(
        "default" to DesignScheme(
            displayName = "Стандартный",
            item = "BAMBOO",
            modelData = 101
        )
    ),
    menu = MenuScheme(
        title = "Банк",
        backButton = MenuItemScheme(
            title = "<red>Назад",
            item = "BARRIER",
            modelData = 100,
            description = listOf(),
        ),
        previousPageItem = MenuItemScheme(
            title = "Предыдущая страница",
            item = "ARROW",
            modelData = 100,
            description = listOf()
        ),
        nextPageItem = MenuItemScheme(
            title = "Следующая страница",
            item = "ARROW",
            modelData = 100,
            description = listOf()
        ),
        mainMenu = MainBankMenu(
            cards = MenuItemScheme(
                title = "<gold>Карты",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Здесь вы можете просмотреть",
                    "список всех карт к которые",
                    "вы создали или имеете к ним доступ",
                    "Баланс ваших счетов: <green>{your_balance}",
                    "Баланс доступных счетов: <green>{others_balance}"
                ),
            ),
            transactions = MenuItemScheme(
                title = "<green>Транзакции",
                item = "EMERALD",
                modelData = 100,
                description = listOf(
                    "Список всех транзакций",
                    "включая входящие и исходящие",
                    "переводы и штрафы"
                ),
            ),
            warnings = MenuItemScheme(
                title = "<red>Штрафы",
                item = "BARRIER",
                modelData = 100,
                description = listOf(
                    "Список всех штрафов",
                    "они выдаются вам за нарушения",
                    "правил и закона",
                ),
            )
        ),
        confirmDeletionMenu = ConfirmDeletion(
            card = ConfirmItemDeletion(
                title = "Подтвердить удаление #{number}",
                confirm = MenuItemScheme(
                    title = "<red>Подтвердить удаление",
                    item = "RED_DYE",
                    modelData = 100,
                    description = listOf()
                ),
                cancel = MenuItemScheme(
                    title = "<red>Отменить",
                    item = "GRAY_DYE",
                    modelData = 100,
                    description = listOf()
                ),
            ),
            subuser = ConfirmItemDeletion(
                title = "Подтвердить удаление?",
                confirm = MenuItemScheme(
                    title = "<red>Подтвердить удаление",
                    item = "RED_DYE",
                    modelData = 100,
                    description = listOf()
                ),
                cancel = MenuItemScheme(
                    title = "<red>Отменить",
                    item = "GRAY_DYE",
                    modelData = 100,
                    description = listOf()
                ),
            ),
        ),
        cardsListMenu = CardsListMenu(
            title = "Счета",
            cardSelectorTitle = "Выберите счет для пополнения",
            cardItem = CardItem(
                cardName = "Карта #{number}",
                description = listOf(
                    "Владелец: {owner}",
                    "Баланс: {balance}",
                    "Основной: {is_main}",
                    "Заблокирован: {is_blocked}"
                )
            ),
            switchToOwned = MenuItemScheme(
                title = "Отображение: <red>Общие счета",
                item = "ENDER_PEARL",
                modelData = 100,
                description = listOf(
                    "Нажмите чтобы переключиться на",
                    "просмотр ваших счетов"
                )
            ),
            switchToCoowned = MenuItemScheme(
                title = "Отображение: <green> Ваши счета",
                item = "ENDER_EYE",
                modelData = 100,
                description = listOf(
                    "Нажмите чтобы переключиться на",
                    "просмотр общих счетов"
                )
            )
        ),
        depositMenu = DepositMenu(
            title = "Операции со счетом #{number}",
            balanceItem = MenuItemScheme(
                title = "<yellow>Баланс: <aqua>{balance}{currency_name}",
                item = "EMERALD",
                modelData = 100,
                description = listOf()
            ),
            deposit1 = MenuItemScheme(
                title = "Пополнить на 1{currency_name}",
                item = "GOLD_NUGGET",
                modelData = 100,
                description = listOf()
            ),
            deposit10 = MenuItemScheme(
                title = "Пополнить на 10{currency_name}",
                item = "RAW_GOLD",
                modelData = 100,
                description = listOf()
            ),
            deposit64 = MenuItemScheme(
                title = "Пополнить на 64{currency_name}",
                item = "RAW_GOLD_BLOCK",
                modelData = 100,
                description = listOf()
            ),
            withdraw1 = MenuItemScheme(
                title = "Снять со счета 1{currency_name}",
                item = "IRON_NUGGET",
                modelData = 100,
                description = listOf()
            ),
            withdraw10 = MenuItemScheme(
                title = "Снять со счета 10{currency_name}",
                item = "RAW_IRON",
                modelData = 100,
                description = listOf()
            ),
            withdraw64 = MenuItemScheme(
                title = "Снять со счета 64{currency_name}",
                item = "RAW_IRON_BLOCK",
                modelData = 100,
                description = listOf()
            ),
        ),
        transactionsListMenu = TransactionsListMenu(
            title = "Список транзакций",
            transferType = "Перевод",
            warnType = "Штраф",
            depositType = "Пополнение",
            transactionItem = MenuItemScheme(
                title = "Транзакция #{number}",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Тип: {type}",
                    "Отправитель: <yellow>{sender}(#{sender_card})",
                    "Получатель: <yellow>{receiver}(#{receiver_card})",
                    "Сумма: <green>{amount}"
                )
            ),
            switchToOwned = MenuItemScheme(
                title = "Отображение: <red>Транзакции общих счетов",
                item = "ENDER_PEARL",
                modelData = 100,
                description = listOf(
                    "Нажмите чтобы переключиться на",
                    "просмотр транзакций ваших счетов"
                )
            ),
            switchToCoowned = MenuItemScheme(
                title = "Отображение: <green>Транзакции ваших счетов",
                item = "ENDER_EYE",
                modelData = 100,
                description = listOf(
                    "Нажмите чтобы переключиться на",
                    "просмотр транзакций общих счетов"
                )
            )
        ),
        warningsMenu = WarningsMenu(
            title = "Штрафы",
            listTitle = "Штрафы игрока {player}",
            paidState = "<green>Оплачен",
            pendingState = "<red>Ожидает оплаты",
            warningItem = MenuItemScheme(
                title = "Штраф #{number}",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Выдал: {issuer}",
                    "Сумма: {amount}",
                    "Статус: {status}",
                    "Причина: {description}"
                )
            )
        ),
        cardActionsMenu = CardActions(
            title = "Управление счетом #{number}",
            subusers =  MenuItemScheme(
                title = "Список совладельцев",
                item = "PLAYER_HEAD",
                modelData = 100,
                description = listOf(
                    "Список всех игроков, которые",
                    "имеют доступ к данному счету"
                )
            ),
            makeMain = MenuItemScheme(
                title = "<yellow>Сделать основным",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Сделать счет основным"
                )
            ),
            lock = MenuItemScheme(
                title = "<red>Заблокировать",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Заблокировать все операции по счету"
                )
            ),
            unlock = MenuItemScheme(
                title = "<green>Разблокировать",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Снять блокировку операций по счету"
                )
            ),
            delete = MenuItemScheme(
                title = "<red>Удалить счет",
                item = "PAPER",
                modelData = 100,
                description = listOf(
                    "Полностью избавиться от счета"
                )
            ),
        ),
        subusersMenu = SubusersMenu(
            title = "Совладельцы счета",
            playerItem = MenuHeadScheme(
                title = "<yellow>{nickname}",
                description = listOf(
                    "Нажмите чтобы удалить совладельца"
                )
            )
        )
    ),
    messages = MessagesScheme(
        configReloaded = "{prefix} Конфиг успешно перезагружен",
        cardCreated = "{prefix} <green>Карта #{number} успешно создана",
        designChanged = "{prefix} <green>Дизайн карты успешно изменён",
        paymentSent = "{prefix} Вы успешно отправили {amount}{currency_name} игроку {player}",
        transferSent = "{prefix} Вы успешно отправили {amount}{currency_name} на счёт #{account}",
        receivedPayment = "{prefix} Вы получили {amount}{currency_name} от {sender} на счёт #{account}",
        comissionTaken = "{prefix} С вас удержана комиссия в размере {amount}{currency_name}",
        moneyTaken = "{prefix} С вашего основного счета было списано {amount}{currency_name}",
        successfullyTakenMoney = "{prefix} Средства были успешно списаны со счета игрока {player}",
        warnCreated = "{prefix} Вы успешно выдали предупреждение #{number} игроку {player}",
        warnReceived = "{prefix} Вам выстален штраф #{number} игроком {player} на сумму {amount}{currency_name} по причине: {description}",
        warnDeleted = "{prefix} Штраф #{number} успешно снят",
        warnPaid = "{prefix} Вы успешно оплатили штраф #{number}",
        warnsUnpaid = "{prefix} У вас есть {number} неоплаченных штрафов на сумму {amount}{currency_name}",
        subuserAdded = "{prefix} <green>Пользователь {nickname} был успешно добавлен как совладелец для #{account}",
        subuserRemoved = "{prefix} <red>Пользователь {nickname} был успешно удален из совладельцев счёта #{account}",
        `true` = "<green>Да",
        `false` = "<red>Нет",
        commandsUsage = CommandsUsage(
            payCommand = "{prefix} <red>Использование /ebank pay [ник] [сумма]",
            newcard = "{prefix} <red>Использование /ebank newcard [дизайн]",
            changeDesign = "{prefix} <red>/ebank changedesign [карта] [дизайн]",
            transferCommand = "{prefix} <red>Использование /ebank transfer [откуда] [куда] [сумма]",
            addSubuser = "{prefix} <red>Использование /ebank subuser add [счёт] [ник]",
            removeSubuser = "{prefix} <red>Использование /ebank subuser remove [счёт] [ник]",
            warnCommand = "{prefix} <red>Использование /ebank warn [игрок] [сумма] [причина]",
            unwarnCommand = "{prefix} <red>Использование /ebank unwarn [ID штрафа]",
            paywarnCommand = "{prefix} <red>Использование /ebank paywarn [ID штрафа] [Счёт]",
            warns = "{prefix} <red>Использование /ebank warns [игрок]",
            takeCommand = "{prefix} <red>Использование /ebank take [Ник] [Сумма]",
            viewTransactions = "{prefix} <red>Использование /ebank transactions view [игрок]"
        ),
        errorMessages = ErrorMessagesScheme(
            notEnoughMoneyToTake = "{prefix} У целевого игрока недостаточно средств для списания",
            noPermissionForDesign = "{prefix} У вас нет прав для создания карты с данным дизайном",
            cantAddSubuser = "{prefix} Вы не можете добавить себя как совладельца",
            noCommand = "{prefix} <red>Такой команды не существует",
            minimalAmount = "{prefix} <red>Минимальная сумма для перевода 1{currency_name}",
            noPermission = "{prefix} <red>У вас нет разрешения для этой команды",
            noDesign = "{prefix} <red>Вы должны указать дизайн карты",
            invalidDesign = "{prefix} <red>Такой дизайн карты не существует",
            cardsLimitReached = "{prefix} <red>Вы достигли максимального количества карт",
            insufficientFunds = "{prefix} <red>У вас недостаточно средств",
            notEnoughItems = "{prefix} <red>У вас недостаточно предметов для пополнения счета на данную сумму",
            notEnoughSlots = "{prefix} <red>У вас недостаточно свободных слотов в инвентаре",
            senderHasNoCards = "{prefix} У вас нет карт",
            receiverHasNoCards = "{prefix} У получателя данного перевода нет карт",
            cardDoesNotExist = "{prefix} Данная карта не существует",
            noSuchCard = "{prefix} <red>Данный счет не существует или не пренадлежит вам",
            noSuchPlayerPlayed = "{prefix} <red>Игрок с данным ником никогда не появлялся на сервере",
            subuserExists = "{prefix} <red>Выбранный игрок уже добавлен как совладелец данного счета",
            subuserIsNotExists = "{prefix} <red>Данный игрок не является совладельцем выбранного счета",
            warnDoesNotExist = "{prefix} Такого предупреждения не существует",
            warnIsPaid = "{prefix} Данный штраф уже оплачен",
            cantPayOthersWarn = "{prefix} <red>Вы не можете оплатить чужой штраф",
            receiverCardIsBlocked = "{prefix} <red>Карта получателя заблокирована владельцем, перевод средств невозможен",
            senderCardIsBlocked = "{prefix} <red>Карта с которой вы пытаетесь совершить перевод - заблокирована"
        )
    ),
)