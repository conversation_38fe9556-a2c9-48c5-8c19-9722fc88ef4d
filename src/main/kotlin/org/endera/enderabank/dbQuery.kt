package org.endera.enderabank

import io.papermc.paper.datacomponent.DataComponentTypes
import io.papermc.paper.datacomponent.item.CustomModelData
import net.kyori.adventure.text.Component
import org.bukkit.Material
import org.bukkit.inventory.ItemStack
import org.endera.enderabank.database.db
import org.endera.enderalib.adventure.stringToComponent
import org.endera.enderalib.utils.async.ioDispatcher
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction

suspend fun <T> dbQuery(block: suspend () -> T): T =
    newSuspendedTransaction(ioDispatcher, db) { block() }


data class CustomItem(
    val itemId: String,
    val material: Material,
    val model: String,
    val name: Component,
    val lore2: List<Component>
) {
    fun createItemStack(amount: Int): ItemStack = ItemStack(material, amount).apply {
        editMeta {
            it.displayName(name)
            it.lore(lore2)
        }
        setData(
            DataComponentTypes.CUSTOM_MODEL_DATA,
            CustomModelData.customModelData().addString(model).build()
        )
    }
}
