package org.endera.enderabank

import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.endera.enderabank.commands.menus.warningsMenu
import org.endera.enderabank.configutils.config
import org.endera.enderabank.utils.cparse

fun warns(sender: Player, args: Array<out String>) {
    if (args.size < 2) {
        sender.sendMessage(config.messages.commandsUsage.warns.cparse())
        return
    }
    val targetPlayer = Bukkit.getOfflinePlayer(args[1])
    if (targetPlayer.hasPlayedBefore()) {
        warningsMenu(sender, 0, targetPlayer.name!!)
    } else {
        sender.sendMessage(config.messages.errorMessages.noSuchPlayerPlayed.cparse())
    }
}
