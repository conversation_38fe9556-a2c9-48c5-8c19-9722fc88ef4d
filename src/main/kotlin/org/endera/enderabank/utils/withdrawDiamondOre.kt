package org.endera.enderabank.utils

import org.bukkit.Material
import org.bukkit.entity.Player
import org.bukkit.inventory.Inventory
import org.endera.enderabank.configutils.config

/**
 * Пытается списать указанное количество валюты из инвентаря игрока.
 * Учитывает только один тип валюты.
 *
 * @param player Игрок, у которого списывается валюта.
 * @param amount Количество валюты, которое нужно списать.
 * @return Возвращает true, если валюта успешно списана, иначе false.
 */
fun withdrawSingleCurrency(player: Player, amount: Int): Boolean {
    val inventory = player.inventory
    val currency = Material.getMaterial(config.currencySettings.firstCurrency) ?: Material.DIAMOND

    val hasEnoughCurrency = countItems(inventory, currency) >= amount

    if (hasEnoughCurrency) {
        inventory.removeItems(currency, amount)
        return true
    } else {
        return false
    }
}


/**
 * Пытается списать указанное количество валюты из инвентаря игрока.
 * Учитывает два типа валюты.
 *
 * @param player Игрок, у которого списывается валюта.
 * @param amount Количество валюты, которое нужно списать.
 * @return Возвращает true, если валюта успешно списана, иначе false.
 */
fun withdrawDoubleCurrency(player: Player, amount: Int): Boolean {
    val inventory = player.inventory
    val firstCurrency = Material.getMaterial(config.currencySettings.firstCurrency) ?: Material.DIAMOND_ORE
    val secondCurrency = Material.getMaterial(config.currencySettings.secondCurrency) ?: Material.DEEPSLATE_DIAMOND_ORE

    val firstCurrencyAmount = countItems(inventory, firstCurrency)
    val secondCurrencyAmount = countItems(inventory, secondCurrency)

    val hasEnoughFirstCurrency = firstCurrencyAmount >= amount
    val hasEnoughSecondCurrency = secondCurrencyAmount >= amount

    if (hasEnoughFirstCurrency) {
        inventory.removeItems(firstCurrency, amount)
        return true
    }
    if (hasEnoughSecondCurrency) {
        inventory.removeItems(secondCurrency, amount)
        return true
    }

    val totalCurrencyAmount = firstCurrencyAmount + secondCurrencyAmount

    if (totalCurrencyAmount >= amount) {
        inventory.removeItems(secondCurrency, amount)
        val remainingToRemove = amount - secondCurrencyAmount
        inventory.removeItems(firstCurrency, remainingToRemove)
        return true
    } else {
        return false
    }
}

/**
 * Считает количество определенного материала в инвентаре игрока.
 *
 * @param inventory Инвентарь игрока.
 * @param material Материал для подсчета.
 * @return Количество найденного материала.
 */
fun countItems(inventory: Inventory, material: Material): Int {
    return inventory.contents.filterNotNull().filter { it.type == material }.sumOf { it.amount }
}
