package org.endera.enderabank.utils

import kotlinx.coroutines.coroutineScope
import org.bukkit.entity.Player
import org.endera.enderabank.commands.Perms
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder

suspend fun withdrawCommission(player: Player) = coroutineScope {

    if (!player.hasPermission(Perms.SBP_PERMISSION)) {
        val repo = DBHolder.accountsRepository
        val name = player.name
        val account = repo.getMainAccountByName(name)!!
        if (account.id.value != config.governmentCard) {
            repo.removeBalance(account.id.value, config.commissionAmount)
            repo.addBalance(config.governmentCard, config.commissionAmount)
            player.sendMessage(config.messages.comissionTaken.replace("{amount}", config.commissionAmount.toString()).cparse())
        }
    }

}

suspend fun withdrawCommission(player: Player, card: Int) = coroutineScope {

    if (!player.hasPermission(Perms.SBP_PERMISSION) && card != config.governmentCard) {
        val repo = DBHolder.accountsRepository
        repo.removeBalance(card, config.commissionAmount)
        repo.addBalance(config.governmentCard, config.commissionAmount)
        player.sendMessage(config.messages.comissionTaken.replace("{amount}", config.commissionAmount.toString()).cparse())
    }

}