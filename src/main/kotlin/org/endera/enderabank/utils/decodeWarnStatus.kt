package org.endera.enderabank.utils

import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.schema.WarningState

fun decodeWarnStatus(warnSatus: WarningState): String {
    return when (warnSatus) {
        WarningState.PENDING -> {
            config.menu.warningsMenu.pendingState
        }
        WarningState.PAID -> {
            config.menu.warningsMenu.paidState
        }
    }
}