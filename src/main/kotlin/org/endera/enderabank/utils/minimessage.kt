package org.endera.enderabank.utils

import net.kyori.adventure.text.Component
import org.endera.enderabank.configutils.config
import org.endera.enderalib.adventure.componentToString
import org.endera.enderalib.adventure.stringToComponent

fun String.cparse(): Component {
    return this
        .replace("{prefix}", config.prefix)
        .replace("{currency_name}", config.currencySettings.currencyName)
        .stringToComponent()
}

fun Component.uncparse(): String {
    return this.componentToString()
}