package org.endera.enderabank.utils

import org.bukkit.Material
import org.bukkit.inventory.Inventory

fun Inventory.removeItems(material: Material, amount: Int): Bo<PERSON>an {
    val inventory = this
    var remaining = amount

    for (i in 0 until inventory.size) {
        val item = inventory.getItem(i) ?: continue
        if (item.type != material) continue

        val itemAmount = item.amount
        if (itemAmount <= remaining) {
            remaining -= itemAmount
            inventory.setItem(i, null)
        } else {
            item.amount = itemAmount - remaining
            inventory.setItem(i, item)
            remaining = 0
        }

        if (remaining == 0) break
    }

    // If remaining is zero, the specified amount was successfully removed
    return remaining == 0
}