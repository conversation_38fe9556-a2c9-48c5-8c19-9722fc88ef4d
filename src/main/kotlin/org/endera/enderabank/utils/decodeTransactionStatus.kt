package org.endera.enderabank.utils

import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.schema.TransactionType

fun decodeTransactionType(transactionType: TransactionType): String {
    return when (transactionType) {
        TransactionType.PENALTY -> config.menu.transactionsListMenu.warnType
        TransactionType.TRANSFER -> config.menu.transactionsListMenu.transferType
        TransactionType.DEPOSIT -> config.menu.transactionsListMenu.depositType
    }
}