package org.endera.enderabank.utils

import org.bukkit.Material
import org.bukkit.inventory.ItemFlag
import org.bukkit.inventory.ItemStack
import org.bukkit.inventory.meta.ItemMeta
import org.bukkit.persistence.PersistentDataType
import org.endera.enderabank.configutils.MenuItemScheme
import org.endera.enderalib.adventure.stringToComponent
import org.endera.enderalib.utils.menu.ItemNameKey

fun createMenuItem(
    itemScheme: MenuItemScheme,
    keys: List<ItemNameKey> = listOf(),
    titleProcessor: (String) -> String = { it },
    descriptionProcessor: (String) -> String = { it }
): ItemStack {
    val item = ItemStack(Material.getMaterial(itemScheme.item) ?: Material.STONE)

    val meta = item.itemMeta as ItemMeta
    if (itemScheme.description.isNotEmpty()) {
        meta.lore(
            itemScheme.description
                .map { "<!italic><white>${descriptionProcessor(it)}".stringToComponent() }
        )
    }

    meta.setCustomModelData(itemScheme.modelData)

    keys.forEach {
        meta.persistentDataContainer.set(it.namespacedKey, PersistentDataType.STRING, it.value)
    }

    meta.displayName("<!italic>${titleProcessor(itemScheme.title)}".stringToComponent())
    meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES)
    item.itemMeta = meta
    return item
}
