import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion

plugins {
    kotlin("jvm") version "2.2.0"
    kotlin("plugin.serialization") version "2.2.0"
    id("com.gradleup.shadow") version "8.3.6"
}

group = "org.endera"
version = "1.4.0-SNAPSHOT"

repositories {
    mavenCentral()
//    mavenLocal()
    maven("https://repo.nexomc.com/releases")

    maven("https://repo.codemc.org/repository/maven-public/")

    maven("https://repo.papermc.io/repository/maven-public/")
    maven("https://repo.extendedclip.com/content/repositories/placeholderapi/")
    maven("https://jitpack.io")
    maven {
        name = "noxcrewMavenPublic"
        url = uri("https://maven.noxcrew.com/public")
    }
}

dependencies {
    implementation("dev.jorel:commandapi-bukkit-shade-mojang-mapped:10.1.2")
    compileOnly("dev.jorel:commandapi-bukkit-core:10.1.2")

    compileOnly("org.jetbrains.kotlin:kotlin-stdlib")
    compileOnly("io.papermc.paper:paper-api:1.21.4-R0.1-SNAPSHOT")
    compileOnly("me.clip:placeholderapi:2.11.6")
    compileOnly("net.kyori:adventure-text-minimessage:4.20.0")
    // Local Lib
//    implementation("org.endera.enderalib:enderalib:1.0-SNAPSHOT")
    compileOnly("com.github.Endera-Org:EnderaLib:1.4.5")
    implementation("com.noxcrew.interfaces:interfaces:2.0.1-SNAPSHOT")

}

tasks.shadowJar {
    relocate("com.noxcrew", "org.endera.enderabank.libs.noxcrew")
    relocate("dev.jorel.commandapi", "org.endera.enderabank.commandapi")
    // Exclude Kotlin libraries that are already provided by EnderaLib
    dependencies {
        exclude(dependency("org.jetbrains.kotlin:.*"))
        exclude(dependency("org.jetbrains.kotlinx:.*"))
    }
}

tasks.processResources {
    inputs.property("version", rootProject.version)
    filesMatching("**plugin.yml") {
        expand("version" to rootProject.version)
    }
}

tasks.test {
    useJUnitPlatform()
}

kotlin {
    jvmToolchain(17)
}

kotlin {
    compilerOptions {
        apiVersion.set(KotlinVersion.KOTLIN_2_1)
        jvmTarget.set(JvmTarget.JVM_21)
    }
}

tasks.withType<JavaCompile> {
    targetCompatibility = "21"
}